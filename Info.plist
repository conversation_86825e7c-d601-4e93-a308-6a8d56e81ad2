<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>UIDeviceFamily</key>
	<array>
		<integer>1</integer>
		<integer>2</integer>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>MinimumOSVersion</key>
	<string>15.0</string>
	<key>CFBundleDisplayName</key>
	<string>Anti-Theft Alarm</string>
	<key>CFBundleIdentifier</key>
	<string>com.antitheft.alarm</string>
	<key>CFBundleVersion</key>
	<string>1.0</string>
	<key>CFBundleIconFiles</key>
	<array>
		<string>Icon-60@2x</string>
		<string>Icon-60@3x</string>
		<string>Icon-76</string>
		<string>Icon-76@2x</string>
		<string>Default</string>
		<string>Default@2x</string>
		<string>Default-568h@2x</string>
		<string>Default-Portrait</string>
		<string>Default-Portrait@2x</string>
		<string>Icon-Small-40</string>
		<string>Icon-Small-40@2x</string>
		<string>Icon-Small-40@3x</string>
		<string>Icon-Small</string>
		<string>Icon-Small@2x</string>
		<string>Icon-Small@3x</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>CFBundleName</key>
	<string>AntiTheftAlarm.iOS</string>
	<key>CFBundleShortVersionString</key>
	<string>1.0</string>
	
	<!-- Background Modes -->
	<key>UIBackgroundModes</key>
	<array>
		<string>background-audio</string>
		<string>background-processing</string>
		<string>background-fetch</string>
	</array>
	
	<!-- Privacy Permissions -->
	<key>NSMotionUsageDescription</key>
	<string>This app needs access to motion sensors to detect device movement for anti-theft protection.</string>
	
	<key>NSMicrophoneUsageDescription</key>
	<string>This app needs microphone access to play alarm sounds at maximum volume.</string>
	
	<key>NSFaceIDUsageDescription</key>
	<string>This app uses Face ID to securely disable the anti-theft alarm.</string>
	
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>This app may use location to enhance security features.</string>
	
	<!-- Audio Session Category -->
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>accelerometer</string>
		<string>gyroscope</string>
	</array>
	
	<!-- App Transport Security -->
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
	</dict>
	
	<!-- Status Bar -->
	<key>UIStatusBarHidden</key>
	<false/>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
	
	<!-- Main Interface -->
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	
	<!-- Scene Configuration -->
	<key>UIApplicationSceneManifest</key>
	<dict>
		<key>UIApplicationSupportsMultipleScenes</key>
		<false/>
		<key>UISceneConfigurations</key>
		<dict>
			<key>UIWindowSceneSessionRoleApplication</key>
			<array>
				<dict>
					<key>UISceneConfigurationName</key>
					<string>Default Configuration</string>
					<key>UISceneDelegateClassName</key>
					<string>SceneDelegate</string>
					<key>UISceneStoryboardFile</key>
					<string>Main</string>
				</dict>
			</array>
		</dict>
	</dict>
</dict>
</plist>
