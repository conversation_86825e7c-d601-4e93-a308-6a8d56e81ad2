#!/bin/bash

# Anti-Theft Alarm iOS Build Script
# This script builds the Xamarin.iOS project for different configurations

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Project configuration
PROJECT_NAME="AntiTheftAlarm.iOS"
PROJECT_FILE="$PROJECT_NAME.csproj"
SOLUTION_FILE="$PROJECT_NAME.sln"

echo -e "${GREEN}🚀 Starting build process for Anti-Theft Alarm iOS${NC}"

# Check if project file exists
if [ ! -f "$PROJECT_FILE" ]; then
    echo -e "${RED}❌ Project file $PROJECT_FILE not found${NC}"
    exit 1
fi

# Function to build for specific configuration and platform
build_project() {
    local config=$1
    local platform=$2
    
    echo -e "${YELLOW}📦 Building $config|$platform...${NC}"
    
    msbuild "$PROJECT_FILE" \
        /p:Configuration="$config" \
        /p:Platform="$platform" \
        /p:BuildIpa=true \
        /verbosity:minimal
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ Build successful for $config|$platform${NC}"
    else
        echo -e "${RED}❌ Build failed for $config|$platform${NC}"
        exit 1
    fi
}

# Function to clean project
clean_project() {
    echo -e "${YELLOW}🧹 Cleaning project...${NC}"
    msbuild "$PROJECT_FILE" /t:Clean /verbosity:minimal
    
    # Remove bin and obj directories
    rm -rf bin/
    rm -rf obj/
    
    echo -e "${GREEN}✅ Project cleaned${NC}"
}

# Function to restore NuGet packages
restore_packages() {
    echo -e "${YELLOW}📦 Restoring NuGet packages...${NC}"
    nuget restore "$PROJECT_FILE"
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ Packages restored successfully${NC}"
    else
        echo -e "${RED}❌ Package restoration failed${NC}"
        exit 1
    fi
}

# Function to run tests
run_tests() {
    echo -e "${YELLOW}🧪 Running tests...${NC}"
    
    # Check if test project exists
    if [ -d "Tests" ]; then
        dotnet test Tests/ --verbosity minimal
        
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✅ All tests passed${NC}"
        else
            echo -e "${RED}❌ Some tests failed${NC}"
            exit 1
        fi
    else
        echo -e "${YELLOW}⚠️  No test project found, skipping tests${NC}"
    fi
}

# Function to validate Info.plist
validate_plist() {
    echo -e "${YELLOW}📋 Validating Info.plist...${NC}"
    
    if [ -f "Info.plist" ]; then
        plutil -lint Info.plist
        
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✅ Info.plist is valid${NC}"
        else
            echo -e "${RED}❌ Info.plist validation failed${NC}"
            exit 1
        fi
    else
        echo -e "${RED}❌ Info.plist not found${NC}"
        exit 1
    fi
}

# Function to check code signing
check_code_signing() {
    echo -e "${YELLOW}🔐 Checking code signing configuration...${NC}"
    
    # This is a placeholder - in real scenario, you'd check for:
    # - Valid provisioning profiles
    # - Valid certificates
    # - Proper entitlements
    
    echo -e "${GREEN}✅ Code signing configuration checked${NC}"
}

# Main build process
main() {
    local build_config=${1:-"Debug"}
    local build_platform=${2:-"iPhoneSimulator"}
    local skip_tests=${3:-false}
    
    echo -e "${GREEN}Configuration: $build_config${NC}"
    echo -e "${GREEN}Platform: $build_platform${NC}"
    echo ""
    
    # Step 1: Clean
    clean_project
    
    # Step 2: Validate plist
    validate_plist
    
    # Step 3: Restore packages
    restore_packages
    
    # Step 4: Run tests (if not skipped)
    if [ "$skip_tests" != "true" ]; then
        run_tests
    fi
    
    # Step 5: Check code signing (for device builds)
    if [ "$build_platform" == "iPhone" ]; then
        check_code_signing
    fi
    
    # Step 6: Build project
    build_project "$build_config" "$build_platform"
    
    echo ""
    echo -e "${GREEN}🎉 Build process completed successfully!${NC}"
    
    # Show output location
    echo -e "${YELLOW}📁 Output location: bin/$build_platform/$build_config/${NC}"
    
    if [ "$build_platform" == "iPhone" ] && [ "$build_config" == "Release" ]; then
        echo -e "${GREEN}📱 IPA file should be available for distribution${NC}"
    fi
}

# Help function
show_help() {
    echo "Anti-Theft Alarm iOS Build Script"
    echo ""
    echo "Usage: $0 [configuration] [platform] [skip_tests]"
    echo ""
    echo "Configurations:"
    echo "  Debug (default)"
    echo "  Release"
    echo ""
    echo "Platforms:"
    echo "  iPhoneSimulator (default)"
    echo "  iPhone"
    echo ""
    echo "Options:"
    echo "  skip_tests: true/false (default: false)"
    echo ""
    echo "Examples:"
    echo "  $0                           # Debug build for simulator"
    echo "  $0 Release iPhone           # Release build for device"
    echo "  $0 Debug iPhoneSimulator true # Debug build, skip tests"
    echo ""
    echo "Environment Requirements:"
    echo "  - Xcode with iOS SDK"
    echo "  - Xamarin.iOS"
    echo "  - MSBuild"
    echo "  - NuGet"
}

# Parse command line arguments
case "$1" in
    -h|--help)
        show_help
        exit 0
        ;;
    *)
        main "$@"
        ;;
esac
