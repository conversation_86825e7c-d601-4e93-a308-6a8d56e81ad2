using System;
using System.Threading.Tasks;
using AntiTheftAlarm.iOS.Models;
using AntiTheftAlarm.iOS.Services;
using AntiTheftAlarm.iOS.Configuration;

namespace AntiTheftAlarm.iOS
{
    /// <summary>
    /// برنامج اختبار بسيط لتجربة وظائف التطبيق
    /// </summary>
    public class TestRunner
    {
        private AlarmManagerService _alarmManager;
        private SettingsService _settingsService;

        public TestRunner()
        {
            Console.WriteLine("🚀 بدء تشغيل اختبار تطبيق Anti-Theft Alarm");
            Console.WriteLine("=" * 50);
        }

        public async Task RunAllTests()
        {
            try
            {
                // اختبار 1: تهيئة الخدمات
                await TestServiceInitialization();
                
                // اختبار 2: إعدادات التطبيق
                await TestSettings();
                
                // اختبار 3: نظام الإنذار
                await TestAlarmSystem();
                
                // اختبار 4: نماذج البيانات
                await TestDataModels();
                
                // اختبار 5: التكوين
                await TestConfiguration();
                
                Console.WriteLine("\n🎉 تم إنجاز جميع الاختبارات بنجاح!");
                
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في الاختبار: {ex.Message}");
            }
        }

        private async Task TestServiceInitialization()
        {
            Console.WriteLine("\n📋 اختبار 1: تهيئة الخدمات");
            Console.WriteLine("-" * 30);

            try
            {
                // تهيئة خدمة إدارة الإنذارات
                _alarmManager = new AlarmManagerService();
                Console.WriteLine("✅ تم تهيئة AlarmManagerService");

                // تهيئة خدمة الإعدادات
                _settingsService = new SettingsService();
                Console.WriteLine("✅ تم تهيئة SettingsService");

                // فحص الحالة الأولية
                var initialState = _alarmManager.CurrentState;
                Console.WriteLine($"📊 الحالة الأولية: نشط={initialState.IsActive}, مُفعل={initialState.IsTriggered}");

                // فحص الإعدادات الافتراضية
                var defaultSettings = _alarmManager.Settings;
                Console.WriteLine($"⚙️ الإعدادات الافتراضية: مستوى الحساسية={defaultSettings.SensitivityLevel}");

            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ فشل في تهيئة الخدمات: {ex.Message}");
                throw;
            }
        }

        private async Task TestSettings()
        {
            Console.WriteLine("\n📋 اختبار 2: إعدادات التطبيق");
            Console.WriteLine("-" * 30);

            try
            {
                // اختبار تغيير مستوى الحساسية
                _alarmManager.UpdateSensitivity(SensitivityLevel.High);
                Console.WriteLine("✅ تم تحديث مستوى الحساسية إلى High");

                // اختبار تفعيل أنواع الإنذار
                _alarmManager.EnableAlarmType(AlarmType.ChargerDisconnected, true);
                _alarmManager.EnableAlarmType(AlarmType.DeviceMovement, true);
                Console.WriteLine("✅ تم تفعيل إنذار فصل الشاحن وإنذار الحركة");

                // اختبار حفظ الإعدادات
                var settings = _settingsService.CurrentSettings;
                settings.Volume = 0.9f;
                settings.DarkModeEnabled = true;
                _settingsService.UpdateSettings(settings);
                Console.WriteLine("✅ تم حفظ الإعدادات المخصصة");

                // عرض الإعدادات الحالية
                Console.WriteLine($"📊 مستوى الصوت: {settings.Volume}");
                Console.WriteLine($"📊 الوضع الليلي: {settings.DarkModeEnabled}");

            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ فشل في اختبار الإعدادات: {ex.Message}");
                throw;
            }
        }

        private async Task TestAlarmSystem()
        {
            Console.WriteLine("\n📋 اختبار 3: نظام الإنذار");
            Console.WriteLine("-" * 30);

            try
            {
                // اختبار تفعيل النظام
                Console.WriteLine("🔄 تفعيل نظام الإنذار...");
                _alarmManager.ActivateAlarm();
                
                var state = _alarmManager.CurrentState;
                if (state.IsActive)
                {
                    Console.WriteLine("✅ تم تفعيل نظام الإنذار بنجاح");
                    Console.WriteLine($"📅 وقت التفعيل: {state.ActivatedAt}");
                }

                // محاكاة حدث إنذار
                Console.WriteLine("🔄 محاكاة حدث إنذار...");
                
                // إضافة مستمع للأحداث
                _alarmManager.AlarmTriggered += (sender, e) =>
                {
                    Console.WriteLine($"🚨 تم تفعيل الإنذار: {e.AlarmType}");
                    Console.WriteLine($"📝 التفاصيل: {e.Details}");
                };

                _alarmManager.AlarmStateChanged += (sender, e) =>
                {
                    Console.WriteLine($"🔄 تغيرت حالة الإنذار: {e.Message}");
                };

                // انتظار قليل لمحاكاة العمل
                await Task.Delay(1000);

                // اختبار إيقاف النظام
                Console.WriteLine("🔄 إيقاف نظام الإنذار...");
                _alarmManager.DeactivateAlarm();
                
                if (!_alarmManager.CurrentState.IsActive)
                {
                    Console.WriteLine("✅ تم إيقاف نظام الإنذار بنجاح");
                }

            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ فشل في اختبار نظام الإنذار: {ex.Message}");
                throw;
            }
        }

        private async Task TestDataModels()
        {
            Console.WriteLine("\n📋 اختبار 4: نماذج البيانات");
            Console.WriteLine("-" * 30);

            try
            {
                // اختبار AlarmEvent
                var alarmEvent = new AlarmEvent
                {
                    AlarmType = AlarmType.DeviceMovement,
                    Details = "تم اكتشاف حركة غير مصرح بها",
                    WasDisabled = true,
                    DisableMethod = DisableMethod.FaceID,
                    DurationSeconds = 15
                };
                Console.WriteLine($"✅ تم إنشاء حدث إنذار: {alarmEvent.AlarmType}");

                // اختبار MotionData
                var motionData = new MotionData
                {
                    AccelerationX = 0.5,
                    AccelerationY = 0.3,
                    AccelerationZ = 0.8,
                    RotationX = 0.1,
                    RotationY = 0.2,
                    RotationZ = 0.1
                };
                
                Console.WriteLine($"✅ بيانات الحركة - التسارع: {motionData.AccelerationMagnitude:F3}");
                Console.WriteLine($"✅ بيانات الحركة - الدوران: {motionData.RotationMagnitude:F3}");

                // اختبار AlarmSettings
                var settings = new AlarmSettings();
                Console.WriteLine($"✅ الإعدادات الافتراضية - تأخير الإنذار: {settings.AlarmDelay}s");

            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ فشل في اختبار نماذج البيانات: {ex.Message}");
                throw;
            }
        }

        private async Task TestConfiguration()
        {
            Console.WriteLine("\n📋 اختبار 5: التكوين");
            Console.WriteLine("-" * 30);

            try
            {
                // اختبار إعدادات الحركة
                var lowThreshold = AppConfig.GetMotionThreshold(SensitivityLevel.Low, true);
                var highThreshold = AppConfig.GetMotionThreshold(SensitivityLevel.High, true);
                
                Console.WriteLine($"✅ عتبة الحساسية المنخفضة: {lowThreshold}");
                Console.WriteLine($"✅ عتبة الحساسية العالية: {highThreshold}");

                // اختبار فترات التحديث
                var updateInterval = AppConfig.GetUpdateInterval(SensitivityLevel.Medium);
                var checkInterval = AppConfig.GetCheckInterval(SensitivityLevel.Medium);
                
                Console.WriteLine($"✅ فترة التحديث: {updateInterval}s");
                Console.WriteLine($"✅ فترة الفحص: {checkInterval}s");

                // اختبار الثوابت
                Console.WriteLine($"✅ اسم التطبيق: {AppConfig.AppName}");
                Console.WriteLine($"✅ إصدار التطبيق: {AppConfig.AppVersion}");

            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ فشل في اختبار التكوين: {ex.Message}");
                throw;
            }
        }

        public void ShowUsageInstructions()
        {
            Console.WriteLine("\n📱 تعليمات الاستخدام:");
            Console.WriteLine("=" * 50);
            Console.WriteLine("1. افتح التطبيق");
            Console.WriteLine("2. اختر أوضاع الإنذار المطلوبة:");
            Console.WriteLine("   • إنذار فصل الشاحن");
            Console.WriteLine("   • إنذار الحركة");
            Console.WriteLine("   • إنذار إخراج من الجيب");
            Console.WriteLine("   • إنذار فصل السماعات");
            Console.WriteLine("3. اضغط 'Activate Alarm'");
            Console.WriteLine("4. ضع الجهاز في المكان المطلوب حمايته");
            Console.WriteLine("5. لإيقاف الإنذار: استخدم Face ID/Touch ID أو كلمة المرور");
            Console.WriteLine("\n⚙️ للوصول للإعدادات: اضغط زر 'Settings'");
            Console.WriteLine("📊 لمراجعة السجل: اضغط زر 'History'");
        }

        // نقطة الدخول للاختبار
        public static async Task Main(string[] args)
        {
            var testRunner = new TestRunner();
            
            try
            {
                await testRunner.RunAllTests();
                testRunner.ShowUsageInstructions();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"\n💥 خطأ عام في التطبيق: {ex.Message}");
                Console.WriteLine($"📍 تفاصيل الخطأ: {ex.StackTrace}");
            }
            
            Console.WriteLine("\n👋 انتهى الاختبار. اضغط أي مفتاح للخروج...");
            Console.ReadKey();
        }
    }
}
