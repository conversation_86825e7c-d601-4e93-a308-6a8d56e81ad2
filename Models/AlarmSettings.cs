using System;
using System.Collections.Generic;

namespace AntiTheftAlarm.iOS.Models
{
    /// <summary>
    /// Model representing alarm configuration settings
    /// </summary>
    public class AlarmSettings
    {
        public AlarmSettings()
        {
            EnabledAlarms = new Dictionary<AlarmType, bool>();
            AlarmSoundPath = "default_alarm.mp3";
            Volume = 1.0f;
            RequireAuthentication = true;
            UseVibration = true;
            SensitivityLevel = SensitivityLevel.Medium;
            
            // Initialize all alarm types as disabled by default
            foreach (AlarmType alarmType in Enum.GetValues(typeof(AlarmType)))
            {
                EnabledAlarms[alarmType] = false;
            }
        }

        /// <summary>
        /// Dictionary tracking which alarm types are enabled
        /// </summary>
        public Dictionary<AlarmType, bool> EnabledAlarms { get; set; }

        /// <summary>
        /// Path to the alarm sound file
        /// </summary>
        public string AlarmSoundPath { get; set; }

        /// <summary>
        /// Volume level for the alarm (0.0 to 1.0)
        /// </summary>
        public float Volume { get; set; }

        /// <summary>
        /// Whether authentication is required to disable alarm
        /// </summary>
        public bool RequireAuthentication { get; set; }

        /// <summary>
        /// Whether to use vibration along with sound
        /// </summary>
        public bool UseVibration { get; set; }

        /// <summary>
        /// Sensitivity level for motion detection
        /// </summary>
        public SensitivityLevel SensitivityLevel { get; set; }

        /// <summary>
        /// Password for disabling alarm (if not using biometric)
        /// </summary>
        public string Password { get; set; }

        /// <summary>
        /// Whether to use Face ID/Touch ID for authentication
        /// </summary>
        public bool UseBiometricAuthentication { get; set; }

        /// <summary>
        /// Whether dark mode is enabled
        /// </summary>
        public bool DarkModeEnabled { get; set; }

        /// <summary>
        /// Delay before alarm triggers (in seconds)
        /// </summary>
        public int AlarmDelay { get; set; } = 3;

        /// <summary>
        /// Maximum duration for alarm sound (in seconds, 0 = infinite)
        /// </summary>
        public int MaxAlarmDuration { get; set; } = 0;
    }

    /// <summary>
    /// Sensitivity levels for motion detection
    /// </summary>
    public enum SensitivityLevel
    {
        Low,
        Medium,
        High,
        VeryHigh
    }
}
