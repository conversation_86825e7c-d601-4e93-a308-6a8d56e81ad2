using System;

namespace AntiTheftAlarm.iOS.Models
{
    /// <summary>
    /// Model representing the current state of the alarm system
    /// </summary>
    public class AlarmState
    {
        public AlarmState()
        {
            IsActive = false;
            IsTriggered = false;
            LastUpdated = DateTime.Now;
        }

        /// <summary>
        /// Whether the alarm system is currently active/armed
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// Whether an alarm is currently triggered and sounding
        /// </summary>
        public bool IsTriggered { get; set; }

        /// <summary>
        /// The type of alarm that is currently triggered (if any)
        /// </summary>
        public AlarmType? TriggeredAlarmType { get; set; }

        /// <summary>
        /// When the alarm was last activated
        /// </summary>
        public DateTime? ActivatedAt { get; set; }

        /// <summary>
        /// When the alarm was triggered (if currently triggered)
        /// </summary>
        public DateTime? TriggeredAt { get; set; }

        /// <summary>
        /// Last time the state was updated
        /// </summary>
        public DateTime LastUpdated { get; set; }

        /// <summary>
        /// Current battery level when alarm was activated
        /// </summary>
        public float? BatteryLevelAtActivation { get; set; }

        /// <summary>
        /// Whether the device is currently charging
        /// </summary>
        public bool IsCharging { get; set; }

        /// <summary>
        /// Whether headphones are currently connected
        /// </summary>
        public bool HeadphonesConnected { get; set; }

        /// <summary>
        /// Current motion sensor readings
        /// </summary>
        public MotionData CurrentMotion { get; set; }

        /// <summary>
        /// Baseline motion data when alarm was activated
        /// </summary>
        public MotionData BaselineMotion { get; set; }
    }

    /// <summary>
    /// Motion sensor data
    /// </summary>
    public class MotionData
    {
        public double AccelerationX { get; set; }
        public double AccelerationY { get; set; }
        public double AccelerationZ { get; set; }
        public double RotationX { get; set; }
        public double RotationY { get; set; }
        public double RotationZ { get; set; }
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// Calculate the magnitude of acceleration
        /// </summary>
        public double AccelerationMagnitude => 
            Math.Sqrt(AccelerationX * AccelerationX + 
                     AccelerationY * AccelerationY + 
                     AccelerationZ * AccelerationZ);

        /// <summary>
        /// Calculate the magnitude of rotation
        /// </summary>
        public double RotationMagnitude => 
            Math.Sqrt(RotationX * RotationX + 
                     RotationY * RotationY + 
                     RotationZ * RotationZ);
    }
}
