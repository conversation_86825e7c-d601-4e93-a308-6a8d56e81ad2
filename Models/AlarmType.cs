using System;

namespace AntiTheftAlarm.iOS.Models
{
    /// <summary>
    /// Enum representing different types of anti-theft alarms
    /// </summary>
    public enum AlarmType
    {
        /// <summary>
        /// Alarm triggered when charger is disconnected
        /// </summary>
        ChargerDisconnected,
        
        /// <summary>
        /// Alarm triggered when device is moved (using accelerometer)
        /// </summary>
        DeviceMovement,
        
        /// <summary>
        /// Alarm triggered when device is removed from pocket/bag
        /// </summary>
        PocketRemoval,
        
        /// <summary>
        /// Alarm triggered when headphones are disconnected
        /// </summary>
        HeadphonesDisconnected
    }
}
