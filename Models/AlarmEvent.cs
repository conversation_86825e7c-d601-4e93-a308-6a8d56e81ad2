using System;

namespace AntiTheftAlarm.iOS.Models
{
    /// <summary>
    /// Model representing an alarm event/trigger
    /// </summary>
    public class AlarmEvent
    {
        public AlarmEvent()
        {
            Id = Guid.NewGuid();
            Timestamp = DateTime.Now;
        }

        /// <summary>
        /// Unique identifier for the alarm event
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// Type of alarm that was triggered
        /// </summary>
        public AlarmType AlarmType { get; set; }

        /// <summary>
        /// Timestamp when the alarm was triggered
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// Whether the alarm was successfully disabled
        /// </summary>
        public bool WasDisabled { get; set; }

        /// <summary>
        /// How the alarm was disabled (if applicable)
        /// </summary>
        public DisableMethod? DisableMethod { get; set; }

        /// <summary>
        /// Duration the alarm was active (in seconds)
        /// </summary>
        public int DurationSeconds { get; set; }

        /// <summary>
        /// Additional details about the event
        /// </summary>
        public string Details { get; set; }

        /// <summary>
        /// Location where the event occurred (if available)
        /// </summary>
        public string Location { get; set; }
    }

    /// <summary>
    /// Methods used to disable an alarm
    /// </summary>
    public enum DisableMethod
    {
        Password,
        FaceID,
        TouchID,
        Emergency,
        Timeout
    }
}
