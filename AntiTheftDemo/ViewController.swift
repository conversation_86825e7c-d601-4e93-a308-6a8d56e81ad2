import UIKit
import CoreMotion
import LocalAuthentication
import AVFoundation
import UserNotifications

class ViewController: UIViewController {
    
    // MARK: - UI Elements
    @IBOutlet weak var titleLabel: UILabel!
    @IBOutlet weak var statusLabel: UILabel!
    @IBOutlet weak var activateButton: UIButton!
    @IBOutlet weak var chargerSwitch: UISwitch!
    @IBOutlet weak var motionSwitch: UISwitch!
    @IBOutlet weak var pocketSwitch: UISwitch!
    @IBOutlet weak var headphonesSwitch: UISwitch!
    
    // MARK: - Properties
    private let motionManager = CMMotionManager()
    private var audioPlayer: AVAudioPlayer?
    private var isAlarmActive = false
    private var isAlarmTriggered = false
    private var baselineAcceleration: Double = 0
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupMotionManager()
        print("📱 تم تحميل الواجهة الرئيسية")
    }
    
    // MARK: - UI Setup
    private func setupUI() {
        // تكوين العناصر
        titleLabel.text = "🛡️ Anti-Theft Alarm"
        titleLabel.font = UIFont.boldSystemFont(ofSize: 28)
        
        statusLabel.text = "الإنذار غير مفعل"
        statusLabel.textColor = .systemGray
        
        activateButton.setTitle("تفعيل الإنذار", for: .normal)
        activateButton.backgroundColor = .systemRed
        activateButton.layer.cornerRadius = 25
        activateButton.titleLabel?.font = UIFont.boldSystemFont(ofSize: 18)
        
        // تكوين المفاتيح
        chargerSwitch.isOn = false
        motionSwitch.isOn = true  // مفعل افتراضياً للتجربة
        pocketSwitch.isOn = false
        headphonesSwitch.isOn = false
    }
    
    private func setupMotionManager() {
        if motionManager.isAccelerometerAvailable {
            motionManager.accelerometerUpdateInterval = 0.1
            print("✅ مستشعر التسارع متاح")
        } else {
            print("❌ مستشعر التسارع غير متاح")
        }
    }
    
    // MARK: - Actions
    @IBAction func activateButtonTapped(_ sender: UIButton) {
        if isAlarmActive {
            deactivateAlarm()
        } else {
            activateAlarm()
        }
    }
    
    @IBAction func settingsButtonTapped(_ sender: UIButton) {
        showSettingsAlert()
    }
    
    @IBAction func historyButtonTapped(_ sender: UIButton) {
        showHistoryAlert()
    }
    
    // MARK: - Alarm Management
    private func activateAlarm() {
        isAlarmActive = true
        updateUI()
        
        // بدء مراقبة الحركة إذا كان مفعلاً
        if motionSwitch.isOn {
            startMotionMonitoring()
        }
        
        // محاكاة مراقبة الشاحن
        if chargerSwitch.isOn {
            startChargerMonitoring()
        }
        
        // إرسال إشعار
        sendNotification(title: "🛡️ تم تفعيل الإنذار", body: "جهازك محمي الآن")
        
        print("🔒 تم تفعيل نظام الإنذار")
    }
    
    private func deactivateAlarm() {
        // طلب المصادقة قبل الإيقاف
        authenticateUser { [weak self] success in
            DispatchQueue.main.async {
                if success {
                    self?.performDeactivation()
                } else {
                    self?.showAuthenticationFailedAlert()
                }
            }
        }
    }
    
    private func performDeactivation() {
        isAlarmActive = false
        isAlarmTriggered = false
        stopAlarmSound()
        stopMotionMonitoring()
        updateUI()
        
        sendNotification(title: "✅ تم إيقاف الإنذار", body: "تم إيقاف حماية الجهاز")
        print("🔓 تم إيقاف نظام الإنذار")
    }
    
    // MARK: - Motion Monitoring
    private func startMotionMonitoring() {
        guard motionManager.isAccelerometerAvailable else { return }
        
        // تسجيل القراءة الأساسية
        motionManager.startAccelerometerUpdates(to: .main) { [weak self] data, error in
            guard let self = self, let data = data else { return }
            
            let acceleration = sqrt(pow(data.acceleration.x, 2) + 
                                  pow(data.acceleration.y, 2) + 
                                  pow(data.acceleration.z, 2))
            
            if self.baselineAcceleration == 0 {
                self.baselineAcceleration = acceleration
                print("📊 تم تسجيل القراءة الأساسية: \(String(format: "%.3f", acceleration))")
            } else {
                let difference = abs(acceleration - self.baselineAcceleration)
                
                // إذا تجاوز التغيير العتبة المحددة
                if difference > 0.3 && self.isAlarmActive && !self.isAlarmTriggered {
                    self.triggerAlarm(type: "اكتشاف حركة", details: "تم اكتشاف حركة غير مصرح بها")
                }
            }
        }
        
        print("🔍 بدأت مراقبة الحركة")
    }
    
    private func stopMotionMonitoring() {
        motionManager.stopAccelerometerUpdates()
        baselineAcceleration = 0
        print("⏹️ توقفت مراقبة الحركة")
    }
    
    // MARK: - Charger Monitoring (Simulation)
    private func startChargerMonitoring() {
        // محاكاة مراقبة الشاحن
        DispatchQueue.main.asyncAfter(deadline: .now() + 5) { [weak self] in
            if self?.isAlarmActive == true && self?.chargerSwitch.isOn == true {
                self?.triggerAlarm(type: "فصل الشاحن", details: "تم فصل الشاحن من الجهاز")
            }
        }
        print("🔌 بدأت مراقبة الشاحن (محاكاة)")
    }
    
    // MARK: - Alarm Triggering
    private func triggerAlarm(type: String, details: String) {
        guard !isAlarmTriggered else { return }
        
        isAlarmTriggered = true
        updateUI()
        
        // تشغيل الصوت
        playAlarmSound()
        
        // اهتزاز
        triggerVibration()
        
        // إشعار
        sendNotification(title: "🚨 تم تفعيل الإنذار!", body: "\(type): \(details)")
        
        // عرض تنبيه
        showAlarmAlert(type: type, details: details)
        
        print("🚨 تم تفعيل الإنذار: \(type)")
    }
    
    // MARK: - Audio
    private func playAlarmSound() {
        // إنشاء صوت إنذار بسيط
        guard let url = Bundle.main.url(forResource: "alarm", withExtension: "mp3") else {
            // إذا لم يوجد ملف صوتي، استخدم صوت النظام
            AudioServicesPlaySystemSound(1005) // صوت إنذار النظام
            return
        }
        
        do {
            audioPlayer = try AVAudioPlayer(contentsOf: url)
            audioPlayer?.numberOfLoops = -1 // تكرار لا نهائي
            audioPlayer?.volume = 1.0
            audioPlayer?.play()
        } catch {
            print("❌ فشل في تشغيل الصوت: \(error)")
            AudioServicesPlaySystemSound(1005)
        }
    }
    
    private func stopAlarmSound() {
        audioPlayer?.stop()
        audioPlayer = nil
    }
    
    private func triggerVibration() {
        let impactFeedback = UIImpactFeedbackGenerator(style: .heavy)
        impactFeedback.impactOccurred()
        
        // اهتزاز متكرر
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { [weak self] in
            if self?.isAlarmTriggered == true {
                impactFeedback.impactOccurred()
                self?.triggerVibration()
            }
        }
    }
    
    // MARK: - Authentication
    private func authenticateUser(completion: @escaping (Bool) -> Void) {
        let context = LAContext()
        var error: NSError?
        
        if context.canEvaluatePolicy(.deviceOwnerAuthenticationWithBiometrics, error: &error) {
            context.evaluatePolicy(.deviceOwnerAuthenticationWithBiometrics, 
                                 localizedReason: "قم بالمصادقة لإيقاف الإنذار") { success, error in
                completion(success)
            }
        } else {
            // استخدام رمز المرور
            context.evaluatePolicy(.deviceOwnerAuthentication, 
                                 localizedReason: "أدخل رمز المرور لإيقاف الإنذار") { success, error in
                completion(success)
            }
        }
    }
    
    // MARK: - Notifications
    private func sendNotification(title: String, body: String) {
        let content = UNMutableNotificationContent()
        content.title = title
        content.body = body
        content.sound = .default
        
        let request = UNNotificationRequest(identifier: UUID().uuidString, 
                                          content: content, 
                                          trigger: UNTimeIntervalNotificationTrigger(timeInterval: 0.1, repeats: false))
        
        UNUserNotificationCenter.current().add(request)
    }
    
    // MARK: - UI Updates
    private func updateUI() {
        if isAlarmTriggered {
            statusLabel.text = "🚨 تم تفعيل الإنذار!"
            statusLabel.textColor = .systemRed
            activateButton.setTitle("إيقاف الإنذار", for: .normal)
            activateButton.backgroundColor = .systemOrange
        } else if isAlarmActive {
            statusLabel.text = "🛡️ الإنذار مفعل - جاري المراقبة..."
            statusLabel.textColor = .systemGreen
            activateButton.setTitle("إيقاف الإنذار", for: .normal)
            activateButton.backgroundColor = .systemGreen
        } else {
            statusLabel.text = "الإنذار غير مفعل"
            statusLabel.textColor = .systemGray
            activateButton.setTitle("تفعيل الإنذار", for: .normal)
            activateButton.backgroundColor = .systemRed
        }
        
        // تعطيل المفاتيح عند تفعيل الإنذار
        chargerSwitch.isEnabled = !isAlarmActive
        motionSwitch.isEnabled = !isAlarmActive
        pocketSwitch.isEnabled = !isAlarmActive
        headphonesSwitch.isEnabled = !isAlarmActive
    }
    
    // MARK: - Alerts
    private func showAlarmAlert(type: String, details: String) {
        let alert = UIAlertController(title: "🚨 تم تفعيل الإنذار!", 
                                    message: "\(type)\n\(details)", 
                                    preferredStyle: .alert)
        
        alert.addAction(UIAlertAction(title: "إيقاف الإنذار", style: .destructive) { [weak self] _ in
            self?.deactivateAlarm()
        })
        
        present(alert, animated: true)
    }
    
    private func showAuthenticationFailedAlert() {
        let alert = UIAlertController(title: "فشل المصادقة", 
                                    message: "لم يتم التحقق من الهوية بنجاح", 
                                    preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "موافق", style: .default))
        present(alert, animated: true)
    }
    
    private func showSettingsAlert() {
        let alert = UIAlertController(title: "⚙️ الإعدادات", 
                                    message: "إعدادات التطبيق ستكون متاحة قريباً", 
                                    preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "موافق", style: .default))
        present(alert, animated: true)
    }
    
    private func showHistoryAlert() {
        let alert = UIAlertController(title: "📊 السجل", 
                                    message: "سجل الأحداث سيكون متاحاً قريباً", 
                                    preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "موافق", style: .default))
        present(alert, animated: true)
    }
}
