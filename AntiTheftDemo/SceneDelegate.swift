import UIKit

class SceneDelegate: UIResponder, UIWindowSceneDelegate {

    var window: UIWindow?

    func scene(_ scene: UIScene, willConnectTo session: UISceneSession, options connectionOptions: UIScene.ConnectionOptions) {
        guard let _ = (scene as? UIWindowScene) else { return }
    }

    func sceneDidDisconnect(_ scene: UIScene) {
    }

    func sceneDidBecomeActive(_ scene: UIScene) {
        print("📱 التطبيق أصبح نشطاً")
    }

    func sceneWillResignActive(_ scene: UIScene) {
        print("📱 التطبيق سيصبح غير نشط")
    }

    func sceneWillEnterForeground(_ scene: UIScene) {
        print("📱 التطبيق سيدخل المقدمة")
    }

    func sceneDidEnterBackground(_ scene: UIScene) {
        print("📱 التطبيق دخل الخلفية")
        // هنا يمكن بدء مهام الخلفية
    }
}
