<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="21507" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES" initialViewController="BYZ-38-t0r">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21505"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--View Controller-->
        <scene sceneID="tne-QT-ifu">
            <objects>
                <viewController id="BYZ-38-t0r" customClass="ViewController" customModule="AntiTheftDemo" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="8bC-Xf-vdC">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="🛡️ Anti-Theft Alarm" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="title-label">
                                <rect key="frame" x="20" y="79" width="353" height="34"/>
                                <fontDescription key="fontDescription" type="boldSystem" pointSize="28"/>
                                <color key="textColor" systemColor="labelColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="الإنذار غير مفعل" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="status-label">
                                <rect key="frame" x="20" y="133" width="353" height="21"/>
                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                <color key="textColor" systemColor="systemGrayColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="activate-button">
                                <rect key="frame" x="96.666666666666686" y="184" width="200" height="50"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="200" id="width-constraint"/>
                                    <constraint firstAttribute="height" constant="50" id="height-constraint"/>
                                </constraints>
                                <state key="normal" title="Button"/>
                                <buttonConfiguration key="configuration" style="filled" title="تفعيل الإنذار">
                                    <fontDescription key="titleFontDescription" type="boldSystem" pointSize="18"/>
                                    <color key="baseBackgroundColor" systemColor="systemRedColor"/>
                                </buttonConfiguration>
                                <connections>
                                    <action selector="activateButtonTapped:" destination="BYZ-38-t0r" eventType="touchUpInside" id="activate-action"/>
                                </connections>
                            </button>
                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="20" translatesAutoresizingMaskIntoConstraints="NO" id="switches-stack">
                                <rect key="frame" x="20" y="274" width="353" height="200"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="charger-view">
                                        <rect key="frame" x="0.0" y="0.0" width="353" height="40"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="🔌 إنذار فصل الشاحن" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="charger-label">
                                                <rect key="frame" x="16" y="9.6666666666666679" width="150" height="20.666666666666664"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                <color key="textColor" systemColor="labelColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <switch opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="750" verticalHuggingPriority="750" contentHorizontalAlignment="center" contentVerticalAlignment="center" translatesAutoresizingMaskIntoConstraints="NO" id="charger-switch">
                                                <rect key="frame" x="304" y="4.6666666666666679" width="51" height="31"/>
                                            </switch>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="secondarySystemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstItem="charger-label" firstAttribute="leading" secondItem="charger-view" secondAttribute="leading" constant="16" id="charger-label-leading"/>
                                            <constraint firstItem="charger-label" firstAttribute="centerY" secondItem="charger-view" secondAttribute="centerY" id="charger-label-center"/>
                                            <constraint firstAttribute="trailing" secondItem="charger-switch" secondAttribute="trailing" constant="16" id="charger-switch-trailing"/>
                                            <constraint firstItem="charger-switch" firstAttribute="centerY" secondItem="charger-view" secondAttribute="centerY" id="charger-switch-center"/>
                                            <constraint firstAttribute="height" constant="40" id="charger-height"/>
                                        </constraints>
                                    </view>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="motion-view">
                                        <rect key="frame" x="0.0" y="60" width="353" height="40"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="📱 إنذار الحركة" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="motion-label">
                                                <rect key="frame" x="16" y="9.6666666666666679" width="120" height="20.666666666666664"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                <color key="textColor" systemColor="labelColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <switch opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="750" verticalHuggingPriority="750" contentHorizontalAlignment="center" contentVerticalAlignment="center" on="YES" translatesAutoresizingMaskIntoConstraints="NO" id="motion-switch">
                                                <rect key="frame" x="304" y="4.6666666666666679" width="51" height="31"/>
                                            </switch>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="secondarySystemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstItem="motion-label" firstAttribute="leading" secondItem="motion-view" secondAttribute="leading" constant="16" id="motion-label-leading"/>
                                            <constraint firstItem="motion-label" firstAttribute="centerY" secondItem="motion-view" secondAttribute="centerY" id="motion-label-center"/>
                                            <constraint firstAttribute="trailing" secondItem="motion-switch" secondAttribute="trailing" constant="16" id="motion-switch-trailing"/>
                                            <constraint firstItem="motion-switch" firstAttribute="centerY" secondItem="motion-view" secondAttribute="centerY" id="motion-switch-center"/>
                                            <constraint firstAttribute="height" constant="40" id="motion-height"/>
                                        </constraints>
                                    </view>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="pocket-view">
                                        <rect key="frame" x="0.0" y="120" width="353" height="40"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="👖 إنذار إخراج من الجيب" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="pocket-label">
                                                <rect key="frame" x="16" y="9.6666666666666679" width="180" height="20.666666666666664"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                <color key="textColor" systemColor="labelColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <switch opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="750" verticalHuggingPriority="750" contentHorizontalAlignment="center" contentVerticalAlignment="center" translatesAutoresizingMaskIntoConstraints="NO" id="pocket-switch">
                                                <rect key="frame" x="304" y="4.6666666666666679" width="51" height="31"/>
                                            </switch>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="secondarySystemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstItem="pocket-label" firstAttribute="leading" secondItem="pocket-view" secondAttribute="leading" constant="16" id="pocket-label-leading"/>
                                            <constraint firstItem="pocket-label" firstAttribute="centerY" secondItem="pocket-view" secondAttribute="centerY" id="pocket-label-center"/>
                                            <constraint firstAttribute="trailing" secondItem="pocket-switch" secondAttribute="trailing" constant="16" id="pocket-switch-trailing"/>
                                            <constraint firstItem="pocket-switch" firstAttribute="centerY" secondItem="pocket-view" secondAttribute="centerY" id="pocket-switch-center"/>
                                            <constraint firstAttribute="height" constant="40" id="pocket-height"/>
                                        </constraints>
                                    </view>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="headphones-view">
                                        <rect key="frame" x="0.0" y="180" width="353" height="40"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="🎧 إنذار فصل السماعات" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="headphones-label">
                                                <rect key="frame" x="16" y="9.6666666666666679" width="170" height="20.666666666666664"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                <color key="textColor" systemColor="labelColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <switch opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="750" verticalHuggingPriority="750" contentHorizontalAlignment="center" contentVerticalAlignment="center" translatesAutoresizingMaskIntoConstraints="NO" id="headphones-switch">
                                                <rect key="frame" x="304" y="4.6666666666666679" width="51" height="31"/>
                                            </switch>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="secondarySystemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstItem="headphones-label" firstAttribute="leading" secondItem="headphones-view" secondAttribute="leading" constant="16" id="headphones-label-leading"/>
                                            <constraint firstItem="headphones-label" firstAttribute="centerY" secondItem="headphones-view" secondAttribute="centerY" id="headphones-label-center"/>
                                            <constraint firstAttribute="trailing" secondItem="headphones-switch" secondAttribute="trailing" constant="16" id="headphones-switch-trailing"/>
                                            <constraint firstItem="headphones-switch" firstAttribute="centerY" secondItem="headphones-view" secondAttribute="centerY" id="headphones-switch-center"/>
                                            <constraint firstAttribute="height" constant="40" id="headphones-height"/>
                                        </constraints>
                                    </view>
                                </subviews>
                            </stackView>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="settings-button">
                                <rect key="frame" x="20" y="754" width="353" height="44"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="44" id="settings-height"/>
                                </constraints>
                                <state key="normal" title="Button"/>
                                <buttonConfiguration key="configuration" style="plain" title="⚙️ الإعدادات"/>
                                <connections>
                                    <action selector="settingsButtonTapped:" destination="BYZ-38-t0r" eventType="touchUpInside" id="settings-action"/>
                                </connections>
                            </button>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="history-button">
                                <rect key="frame" x="20" y="708" width="353" height="44"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="44" id="history-height"/>
                                </constraints>
                                <state key="normal" title="Button"/>
                                <buttonConfiguration key="configuration" style="plain" title="📊 السجل"/>
                                <connections>
                                    <action selector="historyButtonTapped:" destination="BYZ-38-t0r" eventType="touchUpInside" id="history-action"/>
                                </connections>
                            </button>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="6Tk-OE-BBY"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="title-label" firstAttribute="top" secondItem="6Tk-OE-BBY" secondAttribute="top" constant="20" id="title-top"/>
                            <constraint firstItem="title-label" firstAttribute="leading" secondItem="6Tk-OE-BBY" secondAttribute="leading" constant="20" id="title-leading"/>
                            <constraint firstItem="6Tk-OE-BBY" firstAttribute="trailing" secondItem="title-label" secondAttribute="trailing" constant="20" id="title-trailing"/>
                            <constraint firstItem="status-label" firstAttribute="top" secondItem="title-label" secondAttribute="bottom" constant="20" id="status-top"/>
                            <constraint firstItem="status-label" firstAttribute="leading" secondItem="6Tk-OE-BBY" secondAttribute="leading" constant="20" id="status-leading"/>
                            <constraint firstItem="6Tk-OE-BBY" firstAttribute="trailing" secondItem="status-label" secondAttribute="trailing" constant="20" id="status-trailing"/>
                            <constraint firstItem="activate-button" firstAttribute="top" secondItem="status-label" secondAttribute="bottom" constant="30" id="button-top"/>
                            <constraint firstItem="activate-button" firstAttribute="centerX" secondItem="8bC-Xf-vdC" secondAttribute="centerX" id="button-center"/>
                            <constraint firstItem="switches-stack" firstAttribute="top" secondItem="activate-button" secondAttribute="bottom" constant="40" id="stack-top"/>
                            <constraint firstItem="switches-stack" firstAttribute="leading" secondItem="6Tk-OE-BBY" secondAttribute="leading" constant="20" id="stack-leading"/>
                            <constraint firstItem="6Tk-OE-BBY" firstAttribute="trailing" secondItem="switches-stack" secondAttribute="trailing" constant="20" id="stack-trailing"/>
                            <constraint firstItem="6Tk-OE-BBY" firstAttribute="bottom" secondItem="settings-button" secondAttribute="bottom" constant="20" id="settings-bottom"/>
                            <constraint firstItem="settings-button" firstAttribute="leading" secondItem="6Tk-OE-BBY" secondAttribute="leading" constant="20" id="settings-leading"/>
                            <constraint firstItem="6Tk-OE-BBY" firstAttribute="trailing" secondItem="settings-button" secondAttribute="trailing" constant="20" id="settings-trailing"/>
                            <constraint firstItem="settings-button" firstAttribute="top" secondItem="history-button" secondAttribute="bottom" constant="2" id="settings-history-gap"/>
                            <constraint firstItem="history-button" firstAttribute="leading" secondItem="6Tk-OE-BBY" secondAttribute="leading" constant="20" id="history-leading"/>
                            <constraint firstItem="6Tk-OE-BBY" firstAttribute="trailing" secondItem="history-button" secondAttribute="trailing" constant="20" id="history-trailing"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="titleLabel" destination="title-label" id="title-outlet"/>
                        <outlet property="statusLabel" destination="status-label" id="status-outlet"/>
                        <outlet property="activateButton" destination="activate-button" id="activate-outlet"/>
                        <outlet property="chargerSwitch" destination="charger-switch" id="charger-outlet"/>
                        <outlet property="motionSwitch" destination="motion-switch" id="motion-outlet"/>
                        <outlet property="pocketSwitch" destination="pocket-switch" id="pocket-outlet"/>
                        <outlet property="headphonesSwitch" destination="headphones-switch" id="headphones-outlet"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="dkx-z0-nzr" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="20" y="84"/>
        </scene>
    </scenes>
    <resources>
        <systemColor name="labelColor">
            <color white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
        <systemColor name="secondarySystemBackgroundColor">
            <color red="0.94901960784313721" green="0.94901960784313721" blue="0.96862745098039216" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </systemColor>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
        <systemColor name="systemGrayColor">
            <color red="0.55686274509803924" green="0.55686274509803924" blue="0.57647058823529407" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </systemColor>
        <systemColor name="systemRedColor">
            <color red="1" green="0.23137254901960785" blue="0.18823529411764706" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </systemColor>
    </resources>
</document>
