import UIKit
import CoreMotion
import LocalAuthentication
import AVFoundation
import UserNotifications

@main
class AppDelegate: UIResponder, UIApplicationDelegate {

    func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?) -> Bool {
        
        // طلب صلاحيات الإشعارات
        requestNotificationPermissions()
        
        // تكوين جلسة الصوت
        configureAudioSession()
        
        print("🚀 تم تشغيل تطبيق Anti-Theft Alarm")
        
        return true
    }
    
    private func requestNotificationPermissions() {
        let center = UNUserNotificationCenter.current()
        center.requestAuthorization(options: [.alert, .sound, .badge]) { granted, error in
            if granted {
                print("✅ تم منح صلاحيات الإشعارات")
            } else {
                print("❌ تم رفض صلاحيات الإشعارات")
            }
        }
    }
    
    private func configureAudioSession() {
        do {
            let audioSession = AVAudioSession.sharedInstance()
            try audioSession.setCategory(.playback, mode: .default, options: [.defaultToSpeaker])
            try audioSession.setActive(true)
            print("✅ تم تكوين جلسة الصوت")
        } catch {
            print("❌ فشل في تكوين جلسة الصوت: \(error)")
        }
    }

    // MARK: UISceneSession Lifecycle

    func application(_ application: UIApplication, configurationForConnecting connectingSceneSession: UISceneSession, options: UIScene.ConnectionOptions) -> UISceneConfiguration {
        return UISceneConfiguration(name: "Default Configuration", sessionRole: connectingSceneSession.role)
    }

    func application(_ application: UIApplication, didDiscardSceneSessions sceneSessions: Set<UISceneSession>) {
    }
}
