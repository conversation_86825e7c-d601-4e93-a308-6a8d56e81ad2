{"format": 1, "restore": {"/Users/<USER>/Projects/antitheft/AntiTheftAlarm.iOS.csproj": {}}, "projects": {"/Users/<USER>/Projects/antitheft/AntiTheftAlarm.iOS.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Projects/antitheft/AntiTheftAlarm.iOS.csproj", "projectName": "AntiTheftAlarm.iOS", "projectPath": "/Users/<USER>/Projects/antitheft/AntiTheftAlarm.iOS.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Projects/antitheft/obj/", "projectStyle": "PackageReference", "skipContentFileWrite": true, "configFilePaths": ["/Users/<USER>/.config/NuGet/NuGet.Config"], "originalTargetFrameworks": ["xamarinios10"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"xamarinios10": {"projectReferences": {}}}}, "frameworks": {"xamarinios10": {"dependencies": {"Xamarin.Essentials": {"target": "Package", "version": "[1.7.0, )"}}}}, "runtimes": {"win": {"#import": []}, "win-x64": {"#import": []}, "win-x86": {"#import": []}}}}}