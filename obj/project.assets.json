{"version": 3, "targets": {"Xamarin.iOS,Version=v1.0": {"System.Numerics.Vectors/4.5.0": {"type": "package", "compile": {"ref/xamarinios10/_._": {}}, "runtime": {"lib/xamarinios10/_._": {}}}, "Xamarin.Essentials/1.7.0": {"type": "package", "dependencies": {"System.Numerics.Vectors": "4.5.0"}, "frameworkAssemblies": ["OpenTK-1.0", "System.Drawing.Common.dll", "System.Numerics"], "compile": {"lib/xamarinios10/Xamarin.Essentials.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/xamarinios10/Xamarin.Essentials.dll": {"related": ".pdb;.xml"}}}}, "Xamarin.iOS,Version=v1.0/win": {"System.Numerics.Vectors/4.5.0": {"type": "package", "compile": {"ref/xamarinios10/_._": {}}, "runtime": {"lib/xamarinios10/_._": {}}}, "Xamarin.Essentials/1.7.0": {"type": "package", "dependencies": {"System.Numerics.Vectors": "4.5.0"}, "frameworkAssemblies": ["OpenTK-1.0", "System.Drawing.Common.dll", "System.Numerics"], "compile": {"lib/xamarinios10/Xamarin.Essentials.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/xamarinios10/Xamarin.Essentials.dll": {"related": ".pdb;.xml"}}}}, "Xamarin.iOS,Version=v1.0/win-x64": {"System.Numerics.Vectors/4.5.0": {"type": "package", "compile": {"ref/xamarinios10/_._": {}}, "runtime": {"lib/xamarinios10/_._": {}}}, "Xamarin.Essentials/1.7.0": {"type": "package", "dependencies": {"System.Numerics.Vectors": "4.5.0"}, "frameworkAssemblies": ["OpenTK-1.0", "System.Drawing.Common.dll", "System.Numerics"], "compile": {"lib/xamarinios10/Xamarin.Essentials.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/xamarinios10/Xamarin.Essentials.dll": {"related": ".pdb;.xml"}}}}, "Xamarin.iOS,Version=v1.0/win-x86": {"System.Numerics.Vectors/4.5.0": {"type": "package", "compile": {"ref/xamarinios10/_._": {}}, "runtime": {"lib/xamarinios10/_._": {}}}, "Xamarin.Essentials/1.7.0": {"type": "package", "dependencies": {"System.Numerics.Vectors": "4.5.0"}, "frameworkAssemblies": ["OpenTK-1.0", "System.Drawing.Common.dll", "System.Numerics"], "compile": {"lib/xamarinios10/Xamarin.Essentials.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/xamarinios10/Xamarin.Essentials.dll": {"related": ".pdb;.xml"}}}}}, "libraries": {"System.Numerics.Vectors/4.5.0": {"sha512": "QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "type": "package", "path": "system.numerics.vectors/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Numerics.Vectors.dll", "lib/net46/System.Numerics.Vectors.xml", "lib/netcoreapp2.0/_._", "lib/netstandard1.0/System.Numerics.Vectors.dll", "lib/netstandard1.0/System.Numerics.Vectors.xml", "lib/netstandard2.0/System.Numerics.Vectors.dll", "lib/netstandard2.0/System.Numerics.Vectors.xml", "lib/portable-net45+win8+wp8+wpa81/System.Numerics.Vectors.dll", "lib/portable-net45+win8+wp8+wpa81/System.Numerics.Vectors.xml", "lib/uap10.0.16299/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/System.Numerics.Vectors.dll", "ref/net45/System.Numerics.Vectors.xml", "ref/net46/System.Numerics.Vectors.dll", "ref/net46/System.Numerics.Vectors.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.0/System.Numerics.Vectors.dll", "ref/netstandard1.0/System.Numerics.Vectors.xml", "ref/netstandard2.0/System.Numerics.Vectors.dll", "ref/netstandard2.0/System.Numerics.Vectors.xml", "ref/uap10.0.16299/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.numerics.vectors.4.5.0.nupkg.sha512", "system.numerics.vectors.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Xamarin.Essentials/1.7.0": {"sha512": "xfUJNxtfMC05VBej7fVUg6Zd9VwM2kqW9LH0tcrSA6IsBTl0EsIGkYUOz1BoKT2rseWW8o3kTi5WH/yZV4GgJw==", "type": "package", "path": "xamarin.essentials/1.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "icon.png", "lib/monoandroid10.0/Xamarin.Essentials.dll", "lib/monoandroid10.0/Xamarin.Essentials.pdb", "lib/monoandroid10.0/Xamarin.Essentials.xml", "lib/netstandard1.0/Xamarin.Essentials.dll", "lib/netstandard1.0/Xamarin.Essentials.pdb", "lib/netstandard1.0/Xamarin.Essentials.xml", "lib/netstandard2.0/Xamarin.Essentials.dll", "lib/netstandard2.0/Xamarin.Essentials.pdb", "lib/netstandard2.0/Xamarin.Essentials.xml", "lib/tizen40/Xamarin.Essentials.dll", "lib/tizen40/Xamarin.Essentials.pdb", "lib/tizen40/Xamarin.Essentials.xml", "lib/uap10.0.16299/Xamarin.Essentials.dll", "lib/uap10.0.16299/Xamarin.Essentials.pdb", "lib/uap10.0.16299/Xamarin.Essentials.pri", "lib/uap10.0.16299/Xamarin.Essentials.xml", "lib/xamarinios10/Xamarin.Essentials.dll", "lib/xamarinios10/Xamarin.Essentials.pdb", "lib/xamarinios10/Xamarin.Essentials.xml", "lib/xamarinmac20/Xamarin.Essentials.dll", "lib/xamarinmac20/Xamarin.Essentials.pdb", "lib/xamarinmac20/Xamarin.Essentials.xml", "lib/xamarintvos10/Xamarin.Essentials.dll", "lib/xamarintvos10/Xamarin.Essentials.pdb", "lib/xamarintvos10/Xamarin.Essentials.xml", "lib/xamarinwatchos10/Xamarin.Essentials.dll", "lib/xamarinwatchos10/Xamarin.Essentials.pdb", "lib/xamarinwatchos10/Xamarin.Essentials.xml", "readme.txt", "xamarin.essentials.1.7.0.nupkg.sha512", "xamarin.essentials.nuspec"]}}, "projectFileDependencyGroups": {"Xamarin.iOS,Version=v1.0": ["Xamarin.Essentials >= 1.7.0"]}, "packageFolders": {"/Users/<USER>/.nuget/packages/": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Projects/antitheft/AntiTheftAlarm.iOS.csproj", "projectName": "AntiTheftAlarm.iOS", "projectPath": "/Users/<USER>/Projects/antitheft/AntiTheftAlarm.iOS.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Projects/antitheft/obj/", "projectStyle": "PackageReference", "skipContentFileWrite": true, "configFilePaths": ["/Users/<USER>/.config/NuGet/NuGet.Config"], "originalTargetFrameworks": ["xamarinios10"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"xamarinios10": {"projectReferences": {}}}}, "frameworks": {"xamarinios10": {"dependencies": {"Xamarin.Essentials": {"target": "Package", "version": "[1.7.0, )"}}}}, "runtimes": {"win": {"#import": []}, "win-x64": {"#import": []}, "win-x86": {"#import": []}}}}