# Anti-Theft Alarm iOS App

تطبيق iOS متقدم لحماية الجهاز من السرقة باستخدام <PERSON>.iOS

## المميزات الرئيسية

### أوضاع الإنذار المتعددة
- **إنذار فصل الشاحن**: يتم تفعيل الإنذار عند فصل الشاحن من الجهاز
- **إنذار الحركة**: يستخدم مستشعر التسارع والجيروسكوب لاكتشاف حركة الجهاز
- **إنذار إخراج الجهاز من الجيب**: يكتشف الحركة المفاجئة التي تشير إلى إخراج الجهاز من الجيب أو الحقيبة
- **إنذار فصل السماعات**: يتم تفعيل الإنذار عند فصل السماعات السلكية أو اللاسلكية

### الأمان والحماية
- **المصادقة البيومترية**: دعم Face ID و Touch ID لإيقاف الإنذار
- **حماية بكلمة مرور**: إمكانية تعيين كلمة مرور مخصصة
- **صوت عالي محمي**: الإنذار يصدر صوتاً عالياً لا يمكن خفضه بسهولة
- **اهتزاز قوي**: اهتزاز مستمر مع الصوت

### العمل في الخلفية
- **مراقبة مستمرة**: يعمل التطبيق في الخلفية لمراقبة الجهاز
- **Background App Refresh**: دعم تحديث التطبيق في الخلفية
- **Background Processing**: معالجة المهام في الخلفية

### الميزات الإضافية
- **تخصيص الأصوات**: إمكانية اختيار أو إضافة أصوات إنذار مخصصة
- **سجل المحاولات**: تسجيل جميع محاولات التطفل مع التفاصيل والوقت
- **الوضع الليلي**: دعم Dark Mode
- **مستويات الحساسية**: 4 مستويات مختلفة لحساسية اكتشاف الحركة
- **تأخير الإنذار**: إمكانية تعيين تأخير قبل تفعيل الإنذار
- **مدة الإنذار**: تحديد مدة تشغيل الإنذار

## متطلبات النظام

- iOS 15.0 أو أحدث
- Xamarin.iOS
- أجهزة iPhone/iPad مع مستشعرات الحركة
- دعم Face ID أو Touch ID (اختياري)

## البنية التقنية

### النماذج (Models)
- `AlarmType`: أنواع الإنذارات المختلفة
- `AlarmSettings`: إعدادات التطبيق
- `AlarmEvent`: أحداث الإنذار المسجلة
- `AlarmState`: حالة النظام الحالية
- `MotionData`: بيانات مستشعرات الحركة

### الخدمات (Services)
- `AlarmManagerService`: إدارة النظام الرئيسي
- `ChargerMonitoringService`: مراقبة حالة الشاحن
- `MotionMonitoringService`: مراقبة الحركة باستخدام Core Motion
- `HeadphonesMonitoringService`: مراقبة السماعات
- `AlarmSoundService`: تشغيل الأصوات
- `AuthenticationService`: المصادقة البيومترية وكلمة المرور
- `BackgroundTaskService`: العمل في الخلفية
- `SettingsService`: إدارة الإعدادات والبيانات

### واجهات المستخدم (Views)
- `ViewController`: الواجهة الرئيسية
- `SettingsViewController`: شاشة الإعدادات
- `HistoryViewController`: شاشة سجل الأحداث

## كيفية الاستخدام

### التفعيل الأساسي
1. افتح التطبيق
2. اختر أوضاع الإنذار المطلوبة
3. اضغط على "Activate Alarm"
4. ضع الجهاز في المكان المطلوب حمايته

### إيقاف الإنذار
1. عند تفعيل الإنذار، ستظهر رسالة تطلب المصادقة
2. استخدم Face ID/Touch ID أو كلمة المرور
3. سيتم إيقاف الإنذار بعد المصادقة الناجحة

### الإعدادات
- **الأمان**: تفعيل/إيقاف المصادقة البيومترية وتعيين كلمة مرور
- **الحساسية**: اختيار مستوى حساسية اكتشاف الحركة
- **الصوت**: تعديل مستوى الصوت واختيار نغمة الإنذار
- **المظهر**: تفعيل/إيقاف الوضع الليلي
- **متقدم**: تعيين تأخير الإنذار ومدة التشغيل

## الصلاحيات المطلوبة

```xml
<!-- الحركة والمستشعرات -->
<key>NSMotionUsageDescription</key>
<string>This app needs access to motion sensors to detect device movement for anti-theft protection.</string>

<!-- الميكروفون للصوت -->
<key>NSMicrophoneUsageDescription</key>
<string>This app needs microphone access to play alarm sounds at maximum volume.</string>

<!-- Face ID -->
<key>NSFaceIDUsageDescription</key>
<string>This app uses Face ID to securely disable the anti-theft alarm.</string>

<!-- الموقع (اختياري) -->
<key>NSLocationWhenInUseUsageDescription</key>
<string>This app may use location to enhance security features.</string>
```

## العمل في الخلفية

التطبيق يدعم العمل في الخلفية من خلال:

```xml
<key>UIBackgroundModes</key>
<array>
    <string>background-audio</string>
    <string>background-processing</string>
    <string>background-fetch</string>
</array>
```

## البناء والتشغيل

### المتطلبات
- Visual Studio for Mac أو Visual Studio 2022
- Xcode 13.0 أو أحدث
- iOS SDK 15.0 أو أحدث

### خطوات البناء
1. افتح المشروع في Visual Studio
2. تأكد من تحديد الهدف الصحيح (iOS Device أو Simulator)
3. قم ببناء المشروع (Build > Build Solution)
4. شغل التطبيق على الجهاز أو المحاكي

### ملاحظات مهمة
- يجب اختبار التطبيق على جهاز حقيقي للحصول على أفضل أداء للمستشعرات
- بعض الميزات مثل Face ID تتطلب جهاز حقيقي
- العمل في الخلفية محدود بقيود iOS

## الأمان والخصوصية

- جميع البيانات تُحفظ محلياً على الجهاز
- كلمات المرور تُحفظ في iOS Keychain
- لا يتم إرسال أي بيانات إلى خوادم خارجية
- المصادقة البيومترية تستخدم APIs آمنة من Apple

## المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء branch جديد للميزة
3. إضافة التحسينات أو الإصلاحات
4. إرسال Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT. راجع ملف LICENSE للتفاصيل.

## الدعم

للدعم والاستفسارات، يرجى فتح issue في GitHub أو التواصل معنا.

---

**تحذير**: هذا التطبيق مخصص للحماية الشخصية فقط. يرجى استخدامه بمسؤولية وفقاً للقوانين المحلية.
