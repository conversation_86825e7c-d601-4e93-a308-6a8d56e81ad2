using System;
using UIKit;
using Foundation;
using AntiTheftAlarm.iOS.Models;
using AntiTheftAlarm.iOS.Services;
using AntiTheftAlarm.iOS.Views;

namespace AntiTheftAlarm.iOS
{
    public partial class ViewController : UIViewController
    {
        private AlarmManagerService _alarmManager;
        private AlarmSoundService _soundService;
        private AuthenticationService _authService;

        // UI Elements
        private UILabel _titleLabel;
        private UILabel _statusLabel;
        private UIButton _mainToggleButton;
        private UIStackView _alarmTypesStackView;
        private UIButton _settingsButton;
        private UIButton _historyButton;

        // Alarm type switches
        private UISwitch _chargerSwitch;
        private UISwitch _motionSwitch;
        private UISwitch _pocketSwitch;
        private UISwitch _headphonesSwitch;

        public ViewController(IntPtr handle) : base(handle)
        {
        }

        public override void ViewDidLoad()
        {
            base.ViewDidLoad();
            
            InitializeServices();
            SetupUI();
            UpdateUI();
        }

        private void InitializeServices()
        {
            _alarmManager = new AlarmManagerService();
            _soundService = new AlarmSoundService();
            _authService = new AuthenticationService();

            // Subscribe to events
            _alarmManager.AlarmTriggered += OnAlarmTriggered;
            _alarmManager.AlarmStateChanged += OnAlarmStateChanged;
            _soundService.SoundStarted += OnSoundStarted;
            _soundService.SoundStopped += OnSoundStopped;
        }

        private void SetupUI()
        {
            View.BackgroundColor = UIColor.SystemBackground;

            // Title
            _titleLabel = new UILabel
            {
                Text = "Anti-Theft Alarm",
                Font = UIFont.BoldSystemFontOfSize(28),
                TextAlignment = UITextAlignment.Center,
                TextColor = UIColor.Label
            };

            // Status
            _statusLabel = new UILabel
            {
                Text = "Alarm Inactive",
                Font = UIFont.SystemFontOfSize(18),
                TextAlignment = UITextAlignment.Center,
                TextColor = UIColor.SecondaryLabel
            };

            // Main toggle button
            _mainToggleButton = new UIButton(UIButtonType.System)
            {
                BackgroundColor = UIColor.SystemRed,
                TintColor = UIColor.White
            };
            _mainToggleButton.SetTitle("Activate Alarm", UIControlState.Normal);
            _mainToggleButton.Layer.CornerRadius = 25;
            _mainToggleButton.TouchUpInside += OnMainToggleButtonTapped;

            // Alarm types stack view
            _alarmTypesStackView = new UIStackView
            {
                Axis = UILayoutConstraintAxis.Vertical,
                Spacing = 16,
                Distribution = UIStackViewDistribution.FillEqually
            };

            // Create alarm type controls
            CreateAlarmTypeControls();

            // Settings button
            _settingsButton = new UIButton(UIButtonType.System);
            _settingsButton.SetTitle("Settings", UIControlState.Normal);
            _settingsButton.TouchUpInside += OnSettingsButtonTapped;

            // History button
            _historyButton = new UIButton(UIButtonType.System);
            _historyButton.SetTitle("History", UIControlState.Normal);
            _historyButton.TouchUpInside += OnHistoryButtonTapped;

            // Layout
            SetupConstraints();
        }

        private void CreateAlarmTypeControls()
        {
            // Charger disconnection
            var chargerView = CreateAlarmTypeView("Charger Disconnection", out _chargerSwitch);
            _chargerSwitch.ValueChanged += (s, e) => OnAlarmTypeToggled(AlarmType.ChargerDisconnected, _chargerSwitch.On);

            // Device movement
            var motionView = CreateAlarmTypeView("Device Movement", out _motionSwitch);
            _motionSwitch.ValueChanged += (s, e) => OnAlarmTypeToggled(AlarmType.DeviceMovement, _motionSwitch.On);

            // Pocket removal
            var pocketView = CreateAlarmTypeView("Pocket Removal", out _pocketSwitch);
            _pocketSwitch.ValueChanged += (s, e) => OnAlarmTypeToggled(AlarmType.PocketRemoval, _pocketSwitch.On);

            // Headphones disconnection
            var headphonesView = CreateAlarmTypeView("Headphones Disconnection", out _headphonesSwitch);
            _headphonesSwitch.ValueChanged += (s, e) => OnAlarmTypeToggled(AlarmType.HeadphonesDisconnected, _headphonesSwitch.On);

            _alarmTypesStackView.AddArrangedSubview(chargerView);
            _alarmTypesStackView.AddArrangedSubview(motionView);
            _alarmTypesStackView.AddArrangedSubview(pocketView);
            _alarmTypesStackView.AddArrangedSubview(headphonesView);
        }

        private UIView CreateAlarmTypeView(string title, out UISwitch toggle)
        {
            var containerView = new UIView
            {
                BackgroundColor = UIColor.SecondarySystemBackground
            };
            containerView.Layer.CornerRadius = 12;

            var label = new UILabel
            {
                Text = title,
                Font = UIFont.SystemFontOfSize(16),
                TextColor = UIColor.Label
            };

            toggle = new UISwitch
            {
                OnTintColor = UIColor.SystemBlue
            };

            containerView.AddSubview(label);
            containerView.AddSubview(toggle);

            // Constraints
            label.TranslatesAutoresizingMaskIntoConstraints = false;
            toggle.TranslatesAutoresizingMaskIntoConstraints = false;

            NSLayoutConstraint.ActivateConstraints(new[]
            {
                label.LeadingAnchor.ConstraintEqualTo(containerView.LeadingAnchor, 16),
                label.CenterYAnchor.ConstraintEqualTo(containerView.CenterYAnchor),
                toggle.TrailingAnchor.ConstraintEqualTo(containerView.TrailingAnchor, -16),
                toggle.CenterYAnchor.ConstraintEqualTo(containerView.CenterYAnchor),
                containerView.HeightAnchor.ConstraintEqualTo(50)
            });

            return containerView;
        }

        private void SetupConstraints()
        {
            var safeArea = View.SafeAreaLayoutGuide;

            // Add all views
            View.AddSubview(_titleLabel);
            View.AddSubview(_statusLabel);
            View.AddSubview(_mainToggleButton);
            View.AddSubview(_alarmTypesStackView);
            View.AddSubview(_settingsButton);
            View.AddSubview(_historyButton);

            // Disable autoresizing masks
            _titleLabel.TranslatesAutoresizingMaskIntoConstraints = false;
            _statusLabel.TranslatesAutoresizingMaskIntoConstraints = false;
            _mainToggleButton.TranslatesAutoresizingMaskIntoConstraints = false;
            _alarmTypesStackView.TranslatesAutoresizingMaskIntoConstraints = false;
            _settingsButton.TranslatesAutoresizingMaskIntoConstraints = false;
            _historyButton.TranslatesAutoresizingMaskIntoConstraints = false;

            // Activate constraints
            NSLayoutConstraint.ActivateConstraints(new[]
            {
                // Title
                _titleLabel.TopAnchor.ConstraintEqualTo(safeArea.TopAnchor, 20),
                _titleLabel.LeadingAnchor.ConstraintEqualTo(safeArea.LeadingAnchor, 20),
                _titleLabel.TrailingAnchor.ConstraintEqualTo(safeArea.TrailingAnchor, -20),

                // Status
                _statusLabel.TopAnchor.ConstraintEqualTo(_titleLabel.BottomAnchor, 10),
                _statusLabel.LeadingAnchor.ConstraintEqualTo(safeArea.LeadingAnchor, 20),
                _statusLabel.TrailingAnchor.ConstraintEqualTo(safeArea.TrailingAnchor, -20),

                // Main toggle button
                _mainToggleButton.TopAnchor.ConstraintEqualTo(_statusLabel.BottomAnchor, 30),
                _mainToggleButton.CenterXAnchor.ConstraintEqualTo(safeArea.CenterXAnchor),
                _mainToggleButton.WidthAnchor.ConstraintEqualTo(200),
                _mainToggleButton.HeightAnchor.ConstraintEqualTo(50),

                // Alarm types stack view
                _alarmTypesStackView.TopAnchor.ConstraintEqualTo(_mainToggleButton.BottomAnchor, 40),
                _alarmTypesStackView.LeadingAnchor.ConstraintEqualTo(safeArea.LeadingAnchor, 20),
                _alarmTypesStackView.TrailingAnchor.ConstraintEqualTo(safeArea.TrailingAnchor, -20),

                // Settings button
                _settingsButton.BottomAnchor.ConstraintEqualTo(_historyButton.TopAnchor, -10),
                _settingsButton.LeadingAnchor.ConstraintEqualTo(safeArea.LeadingAnchor, 20),
                _settingsButton.TrailingAnchor.ConstraintEqualTo(safeArea.TrailingAnchor, -20),
                _settingsButton.HeightAnchor.ConstraintEqualTo(44),

                // History button
                _historyButton.BottomAnchor.ConstraintEqualTo(safeArea.BottomAnchor, -20),
                _historyButton.LeadingAnchor.ConstraintEqualTo(safeArea.LeadingAnchor, 20),
                _historyButton.TrailingAnchor.ConstraintEqualTo(safeArea.TrailingAnchor, -20),
                _historyButton.HeightAnchor.ConstraintEqualTo(44)
            });
        }

        private void UpdateUI()
        {
            var isActive = _alarmManager.CurrentState.IsActive;
            var isTriggered = _alarmManager.CurrentState.IsTriggered;

            // Update status
            if (isTriggered)
            {
                _statusLabel.Text = $"ALARM TRIGGERED: {_alarmManager.CurrentState.TriggeredAlarmType}";
                _statusLabel.TextColor = UIColor.SystemRed;
            }
            else if (isActive)
            {
                _statusLabel.Text = "Alarm Active - Monitoring...";
                _statusLabel.TextColor = UIColor.SystemGreen;
            }
            else
            {
                _statusLabel.Text = "Alarm Inactive";
                _statusLabel.TextColor = UIColor.SecondaryLabel;
            }

            // Update main button
            _mainToggleButton.SetTitle(isActive ? "Deactivate Alarm" : "Activate Alarm", UIControlState.Normal);
            _mainToggleButton.BackgroundColor = isActive ? UIColor.SystemGreen : UIColor.SystemRed;

            // Update alarm type switches
            var settings = _alarmManager.Settings;
            _chargerSwitch.On = settings.EnabledAlarms[AlarmType.ChargerDisconnected];
            _motionSwitch.On = settings.EnabledAlarms[AlarmType.DeviceMovement];
            _pocketSwitch.On = settings.EnabledAlarms[AlarmType.PocketRemoval];
            _headphonesSwitch.On = settings.EnabledAlarms[AlarmType.HeadphonesDisconnected];

            // Disable switches when alarm is active
            _chargerSwitch.Enabled = !isActive;
            _motionSwitch.Enabled = !isActive;
            _pocketSwitch.Enabled = !isActive;
            _headphonesSwitch.Enabled = !isActive;
        }

        private async void OnMainToggleButtonTapped(object sender, EventArgs e)
        {
            if (_alarmManager.CurrentState.IsActive)
            {
                // Deactivate alarm - require authentication
                await DeactivateAlarmWithAuthentication();
            }
            else
            {
                // Activate alarm
                _alarmManager.ActivateAlarm();
            }
        }

        private async System.Threading.Tasks.Task DeactivateAlarmWithAuthentication()
        {
            var biometricStatus = await _authService.GetBiometricAuthenticationStatusAsync();
            
            if (biometricStatus == BiometricAuthenticationStatus.FaceIDAvailable || 
                biometricStatus == BiometricAuthenticationStatus.TouchIDAvailable)
            {
                var result = await _authService.AuthenticateWithBiometricsAsync();
                if (result.IsSuccessful)
                {
                    _alarmManager.DeactivateAlarm();
                    _soundService.StopAlarm();
                }
                else
                {
                    ShowAuthenticationFailedAlert(result.ErrorMessage);
                }
            }
            else
            {
                // Fall back to device passcode
                var result = await _authService.AuthenticateWithDevicePasscodeAsync();
                if (result.IsSuccessful)
                {
                    _alarmManager.DeactivateAlarm();
                    _soundService.StopAlarm();
                }
                else
                {
                    ShowAuthenticationFailedAlert(result.ErrorMessage);
                }
            }
        }

        private void ShowAuthenticationFailedAlert(string message)
        {
            var alert = UIAlertController.Create("Authentication Failed", message, UIAlertControllerStyle.Alert);
            alert.AddAction(UIAlertAction.Create("OK", UIAlertActionStyle.Default, null));
            PresentViewController(alert, true, null);
        }

        private void OnAlarmTypeToggled(AlarmType alarmType, bool enabled)
        {
            _alarmManager.EnableAlarmType(alarmType, enabled);
        }

        private void OnSettingsButtonTapped(object sender, EventArgs e)
        {
            var settingsViewController = new SettingsViewController();
            var navigationController = new UINavigationController(settingsViewController);
            PresentViewController(navigationController, true, null);
        }

        private void OnHistoryButtonTapped(object sender, EventArgs e)
        {
            var historyViewController = new HistoryViewController();
            var navigationController = new UINavigationController(historyViewController);
            PresentViewController(navigationController, true, null);
        }

        private void OnAlarmTriggered(object sender, AlarmTriggeredEventArgs e)
        {
            InvokeOnMainThread(() =>
            {
                _soundService.PlayAlarm();
                UpdateUI();
                
                // Show alert
                var alert = UIAlertController.Create("ALARM TRIGGERED!", 
                    $"{e.AlarmType} detected at {e.Timestamp:HH:mm:ss}", 
                    UIAlertControllerStyle.Alert);
                alert.AddAction(UIAlertAction.Create("Disable", UIAlertActionStyle.Destructive, async _ =>
                {
                    await DeactivateAlarmWithAuthentication();
                }));
                PresentViewController(alert, true, null);
            });
        }

        private void OnAlarmStateChanged(object sender, AlarmStateChangedEventArgs e)
        {
            InvokeOnMainThread(UpdateUI);
        }

        private void OnSoundStarted(object sender, AlarmSoundEventArgs e)
        {
            Console.WriteLine($"Alarm sound started: {e.SoundFileName}");
        }

        private void OnSoundStopped(object sender, AlarmSoundEventArgs e)
        {
            Console.WriteLine("Alarm sound stopped");
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                _alarmManager?.Dispose();
                _soundService?.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}
