{"Format": 1, "ProjectReferences": [], "MetadataReferences": [{"FilePath": "/Library/Frameworks/Xamarin.iOS.framework/Versions/*********/lib/mono/Xamarin.iOS/mscorlib.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Xamarin.iOS.framework/Versions/*********/lib/mono/Xamarin.iOS/OpenTK-1.0.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Xamarin.iOS.framework/Versions/*********/lib/mono/Xamarin.iOS/System.Core.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Xamarin.iOS.framework/Versions/*********/lib/mono/Xamarin.iOS/System.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Xamarin.iOS.framework/Versions/*********/lib/mono/Xamarin.iOS/Facades/System.Drawing.Common.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Xamarin.iOS.framework/Versions/*********/lib/mono/Xamarin.iOS/System.Numerics.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Xamarin.iOS.framework/Versions/*********/lib/mono/Xamarin.iOS/System.Numerics.Vectors.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Xamarin.iOS.framework/Versions/*********/lib/mono/Xamarin.iOS/System.Xml.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/xamarin.essentials/1.7.0/lib/xamarinios10/Xamarin.Essentials.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Xamarin.iOS.framework/Versions/*********/lib/mono/Xamarin.iOS/Xamarin.iOS.dll", "Aliases": [], "Framework": null}], "Files": ["/Users/<USER>/Projects/antitheft/Main.cs", "/Users/<USER>/Projects/antitheft/AppDelegate.cs", "/Users/<USER>/Projects/antitheft/SceneDelegate.cs", "/Users/<USER>/Projects/antitheft/ViewController.cs", "/Users/<USER>/Projects/antitheft/Properties/AssemblyInfo.cs", "/Users/<USER>/Projects/antitheft/obj/iPhoneSimulator/Debug/Xamarin.iOS,Version=v1.0.AssemblyAttributes.cs", "/Users/<USER>/Projects/antitheft/Entitlements.plist", "/Users/<USER>/Projects/antitheft/Info.plist", "/Users/<USER>/Projects/antitheft/Resources/LaunchScreen.storyboard", "/Users/<USER>/Projects/antitheft/Main.storyboard"], "BuildActions": ["Compile", "Compile", "Compile", "Compile", "Compile", "Compile", "None", "None", "InterfaceDefinition", "InterfaceDefinition"], "Analyzers": [], "AdditionalFiles": [], "EditorConfigFiles": [], "DefineConstants": ["__IOS__", "__MOBILE__", "__UNIFIED__", "DEBUG"], "IntermediateAssembly": "/Users/<USER>/Projects/antitheft/obj/iPhoneSimulator/Debug/AntiTheftAlarm.iOS.exe"}