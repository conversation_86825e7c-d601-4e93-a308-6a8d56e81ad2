using Foundation;
using UIKit;
using UserNotifications;
using AVFoundation;
using AntiTheftAlarm.iOS.Services;

namespace AntiTheftAlarm.iOS
{
    [Register("AppDelegate")]
    public partial class AppDelegate : UIApplicationDelegate
    {
        public override UIWindow Window { get; set; }

        public static AlarmManagerService AlarmManager { get; private set; }
        public static BackgroundTaskService BackgroundTaskService { get; private set; }

        public override bool FinishedLaunching(UIApplication application, NSDictionary launchOptions)
        {
            // Override point for customization after application launch.
            // If not required for your application you can safely delete this method

            // Request notification permissions
            RequestNotificationPermissions();
            
            // Configure audio session for background audio
            ConfigureAudioSession();
            
            // Initialize services
            InitializeServices();

            // Initialize background tasks
            InitializeBackgroundTasks();

            return true;
        }

        private void RequestNotificationPermissions()
        {
            var center = UNUserNotificationCenter.Current;
            center.RequestAuthorization(
                UNAuthorizationOptions.Alert | UNAuthorizationOptions.Sound | UNAuthorizationOptions.Badge,
                (granted, error) =>
                {
                    if (granted)
                    {
                        Console.WriteLine("Notification permissions granted");
                    }
                    else
                    {
                        Console.WriteLine("Notification permissions denied");
                    }
                });
        }

        private void ConfigureAudioSession()
        {
            try
            {
                var audioSession = AVAudioSession.SharedInstance();
                var error = audioSession.SetCategory(AVAudioSessionCategory.Playback, 
                    AVAudioSessionCategoryOptions.MixWithOthers | AVAudioSessionCategoryOptions.DuckOthers);
                
                if (error != null)
                {
                    Console.WriteLine($"Audio session configuration error: {error.LocalizedDescription}");
                }
                
                audioSession.SetActive(true);
            }
            catch (System.Exception ex)
            {
                Console.WriteLine($"Audio session setup failed: {ex.Message}");
            }
        }

        private void InitializeServices()
        {
            // Initialize alarm manager
            AlarmManager = new AlarmManagerService();

            // Initialize background task service
            BackgroundTaskService = new BackgroundTaskService(AlarmManager);
        }

        private void InitializeBackgroundTasks()
        {
            // Register background task identifiers
            UIApplication.SharedApplication.SetMinimumBackgroundFetchInterval(UIApplication.BackgroundFetchIntervalMinimum);
        }

        public override void OnActivated(UIApplication application)
        {
            // Restart any tasks that were paused (or not yet started) while the application was inactive.
            // If the application was previously in the background, optionally refresh the user interface.
        }

        public override void OnResignActivation(UIApplication application)
        {
            // Invoked when the application is about to move from active to inactive state.
            // This can occur for certain types of temporary interruptions (such as an incoming phone call or SMS message) 
            // or when the user quits the application and it begins the transition to the background state.
            // Games should use this method to pause the game.
        }

        public override void DidEnterBackground(UIApplication application)
        {
            // Use this method to release shared resources, save user data, invalidate timers and store the application state.
            // If your application supports background execution this method is called instead of WillTerminate when the user quits.
        }

        public override void WillEnterForeground(UIApplication application)
        {
            // Called as part of the transiton from background to active state.
            // Here you can undo many of the changes made on entering the background.
        }

        public override void WillTerminate(UIApplication application)
        {
            // Called when the application is about to terminate. Save data, if needed. See also DidEnterBackground.
        }

        public override void PerformFetch(UIApplication application, System.Action<UIBackgroundFetchResult> completionHandler)
        {
            // Perform background fetch operations
            completionHandler(UIBackgroundFetchResult.NewData);
        }
    }
}
