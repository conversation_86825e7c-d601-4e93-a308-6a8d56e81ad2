using System;
using System.Collections.Generic;
using System.Linq;
using UIKit;
using Foundation;
using AntiTheftAlarm.iOS.Models;
using AntiTheftAlarm.iOS.Services;

namespace AntiTheftAlarm.iOS.Views
{
    /// <summary>
    /// View controller for alarm history
    /// </summary>
    public class HistoryViewController : UIViewController
    {
        private SettingsService _settingsService;
        private UITableView _tableView;
        private HistoryTableSource _tableSource;
        private List<AlarmEvent> _alarmHistory;

        public HistoryViewController()
        {
            Title = "Alarm History";
        }

        public override void ViewDidLoad()
        {
            base.ViewDidLoad();
            
            _settingsService = new SettingsService();
            LoadHistory();
            SetupUI();
        }

        public override void ViewWillAppear(bool animated)
        {
            base.ViewWillAppear(animated);
            LoadHistory();
            _tableView.ReloadData();
        }

        private void LoadHistory()
        {
            _alarmHistory = _settingsService.LoadAlarmHistory()
                .OrderByDescending(e => e.Timestamp)
                .ToList();
        }

        private void SetupUI()
        {
            View.BackgroundColor = UIColor.SystemBackground;

            // Navigation bar
            NavigationItem.LeftBarButtonItem = new UIBarButtonItem("Close", UIBarButtonItemStyle.Plain, OnCloseTapped);
            NavigationItem.RightBarButtonItem = new UIBarButtonItem("Clear", UIBarButtonItemStyle.Plain, OnClearTapped);

            // Table view
            _tableView = new UITableView(View.Bounds, UITableViewStyle.InsetGrouped);
            _tableSource = new HistoryTableSource(_alarmHistory);
            _tableView.Source = _tableSource;

            // Empty state
            if (_alarmHistory.Count == 0)
            {
                ShowEmptyState();
            }

            View.AddSubview(_tableView);

            // Constraints
            _tableView.TranslatesAutoresizingMaskIntoConstraints = false;
            NSLayoutConstraint.ActivateConstraints(new[]
            {
                _tableView.TopAnchor.ConstraintEqualTo(View.SafeAreaLayoutGuide.TopAnchor),
                _tableView.LeadingAnchor.ConstraintEqualTo(View.LeadingAnchor),
                _tableView.TrailingAnchor.ConstraintEqualTo(View.TrailingAnchor),
                _tableView.BottomAnchor.ConstraintEqualTo(View.BottomAnchor)
            });
        }

        private void ShowEmptyState()
        {
            var emptyLabel = new UILabel
            {
                Text = "No alarm events recorded",
                TextAlignment = UITextAlignment.Center,
                TextColor = UIColor.SecondaryLabel,
                Font = UIFont.SystemFontOfSize(16)
            };

            _tableView.BackgroundView = emptyLabel;
        }

        private void OnCloseTapped(object sender, EventArgs e)
        {
            DismissViewController(true, null);
        }

        private void OnClearTapped(object sender, EventArgs e)
        {
            var alert = UIAlertController.Create("Clear History", "Are you sure you want to clear all alarm history?", UIAlertControllerStyle.Alert);
            
            alert.AddAction(UIAlertAction.Create("Cancel", UIAlertActionStyle.Cancel, null));
            alert.AddAction(UIAlertAction.Create("Clear", UIAlertActionStyle.Destructive, _ =>
            {
                _settingsService.ClearAlarmHistory();
                LoadHistory();
                _tableView.ReloadData();
                
                if (_alarmHistory.Count == 0)
                {
                    ShowEmptyState();
                }
            }));

            PresentViewController(alert, true, null);
        }
    }

    /// <summary>
    /// Table source for alarm history
    /// </summary>
    public class HistoryTableSource : UITableViewSource
    {
        private readonly List<AlarmEvent> _alarmHistory;

        public HistoryTableSource(List<AlarmEvent> alarmHistory)
        {
            _alarmHistory = alarmHistory;
        }

        public override nint RowsInSection(UITableView tableView, nint section)
        {
            return _alarmHistory.Count;
        }

        public override UITableViewCell GetCell(UITableView tableView, NSIndexPath indexPath)
        {
            var cell = tableView.DequeueReusableCell("HistoryCell") ?? new UITableViewCell(UITableViewCellStyle.Subtitle, "HistoryCell");
            var alarmEvent = _alarmHistory[indexPath.Row];

            // Main text
            cell.TextLabel.Text = $"{alarmEvent.AlarmType}";
            
            // Detail text
            var timeText = alarmEvent.Timestamp.ToString("MMM dd, yyyy HH:mm:ss");
            var statusText = alarmEvent.WasDisabled ? $"Disabled via {alarmEvent.DisableMethod}" : "Not disabled";
            var durationText = alarmEvent.DurationSeconds > 0 ? $"Duration: {alarmEvent.DurationSeconds}s" : "";
            
            cell.DetailTextLabel.Text = $"{timeText} • {statusText}";
            if (!string.IsNullOrEmpty(durationText))
            {
                cell.DetailTextLabel.Text += $" • {durationText}";
            }

            // Color coding based on alarm type
            cell.TextLabel.TextColor = GetColorForAlarmType(alarmEvent.AlarmType);
            
            // Accessory
            cell.AccessoryType = UITableViewCellAccessoryType.DisclosureIndicator;

            return cell;
        }

        public override void RowSelected(UITableView tableView, NSIndexPath indexPath)
        {
            tableView.DeselectRow(indexPath, true);
            
            var alarmEvent = _alarmHistory[indexPath.Row];
            ShowAlarmEventDetails(alarmEvent);
        }

        private UIColor GetColorForAlarmType(AlarmType alarmType)
        {
            return alarmType switch
            {
                AlarmType.ChargerDisconnected => UIColor.SystemOrange,
                AlarmType.DeviceMovement => UIColor.SystemRed,
                AlarmType.PocketRemoval => UIColor.SystemPurple,
                AlarmType.HeadphonesDisconnected => UIColor.SystemBlue,
                _ => UIColor.Label
            };
        }

        private void ShowAlarmEventDetails(AlarmEvent alarmEvent)
        {
            var details = $"Alarm Type: {alarmEvent.AlarmType}\n" +
                         $"Time: {alarmEvent.Timestamp:MMM dd, yyyy HH:mm:ss}\n" +
                         $"Duration: {(alarmEvent.DurationSeconds > 0 ? $"{alarmEvent.DurationSeconds} seconds" : "Unknown")}\n" +
                         $"Status: {(alarmEvent.WasDisabled ? $"Disabled via {alarmEvent.DisableMethod}" : "Not disabled")}\n";

            if (!string.IsNullOrEmpty(alarmEvent.Details))
            {
                details += $"Details: {alarmEvent.Details}\n";
            }

            if (!string.IsNullOrEmpty(alarmEvent.Location))
            {
                details += $"Location: {alarmEvent.Location}";
            }

            var alert = UIAlertController.Create("Alarm Event Details", details, UIAlertControllerStyle.Alert);
            alert.AddAction(UIAlertAction.Create("OK", UIAlertActionStyle.Default, null));
            
            GetViewController().PresentViewController(alert, true, null);
        }

        private UIViewController GetViewController()
        {
            // Helper method to get the current view controller
            var window = UIApplication.SharedApplication.KeyWindow;
            return window?.RootViewController?.PresentedViewController ?? window?.RootViewController;
        }
    }
}
