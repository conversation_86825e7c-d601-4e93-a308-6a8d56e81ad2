using System;
using System.IO;
using UIKit;
using Foundation;
using AntiTheftAlarm.iOS.Models;
using AntiTheftAlarm.iOS.Services;

namespace AntiTheftAlarm.iOS.Views
{
    /// <summary>
    /// View controller for app settings
    /// </summary>
    public class SettingsViewController : UIViewController
    {
        private SettingsService _settingsService;
        private UITableView _tableView;
        private SettingsTableSource _tableSource;

        public SettingsViewController()
        {
            Title = "Settings";
        }

        public override void ViewDidLoad()
        {
            base.ViewDidLoad();
            
            _settingsService = new SettingsService();
            SetupUI();
        }

        private void SetupUI()
        {
            View.BackgroundColor = UIColor.SystemBackground;

            // Navigation bar
            NavigationItem.LeftBarButtonItem = new UIBarButtonItem("Cancel", UIBarButtonItemStyle.Plain, OnCancelTapped);
            NavigationItem.RightBarButtonItem = new UIBarButtonItem("Done", UIBarButtonItemStyle.Done, OnDoneTapped);

            // Table view
            _tableView = new UITableView(View.Bounds, UITableViewStyle.InsetGrouped);
            _tableSource = new SettingsTableSource(_settingsService);
            _tableView.Source = _tableSource;

            View.AddSubview(_tableView);

            // Constraints
            _tableView.TranslatesAutoresizingMaskIntoConstraints = false;
            NSLayoutConstraint.ActivateConstraints(new[]
            {
                _tableView.TopAnchor.ConstraintEqualTo(View.SafeAreaLayoutGuide.TopAnchor),
                _tableView.LeadingAnchor.ConstraintEqualTo(View.LeadingAnchor),
                _tableView.TrailingAnchor.ConstraintEqualTo(View.TrailingAnchor),
                _tableView.BottomAnchor.ConstraintEqualTo(View.BottomAnchor)
            });
        }

        private void OnCancelTapped(object sender, EventArgs e)
        {
            DismissViewController(true, null);
        }

        private void OnDoneTapped(object sender, EventArgs e)
        {
            _settingsService.SaveSettings();
            DismissViewController(true, null);
        }
    }

    /// <summary>
    /// Table source for settings
    /// </summary>
    public class SettingsTableSource : UITableViewSource
    {
        private readonly SettingsService _settingsService;
        private readonly string[] _sectionTitles = { "Security", "Sensitivity", "Audio", "Appearance", "Advanced" };

        public SettingsTableSource(SettingsService settingsService)
        {
            _settingsService = settingsService;
        }

        public override nint NumberOfSections(UITableView tableView)
        {
            return _sectionTitles.Length;
        }

        public override nint RowsInSection(UITableView tableView, nint section)
        {
            return section switch
            {
                0 => 2, // Security: Biometric Auth, Password
                1 => 1, // Sensitivity: Level
                2 => 3, // Audio: Volume, Sound, Duration
                3 => 1, // Appearance: Dark Mode
                4 => 3, // Advanced: Delay, Reset, Export
                _ => 0
            };
        }

        public override string TitleForHeader(UITableView tableView, nint section)
        {
            return _sectionTitles[section];
        }

        public override UITableViewCell GetCell(UITableView tableView, NSIndexPath indexPath)
        {
            var cell = tableView.DequeueReusableCell("SettingsCell") ?? new UITableViewCell(UITableViewCellStyle.Value1, "SettingsCell");
            var settings = _settingsService.CurrentSettings;

            switch (indexPath.Section)
            {
                case 0: // Security
                    if (indexPath.Row == 0)
                    {
                        cell.TextLabel.Text = "Use Biometric Authentication";
                        var biometricSwitch = new UISwitch { On = settings.UseBiometricAuthentication };
                        biometricSwitch.ValueChanged += (s, e) =>
                        {
                            _settingsService.SetBiometricAuthentication(biometricSwitch.On);
                        };
                        cell.AccessoryView = biometricSwitch;
                    }
                    else
                    {
                        cell.TextLabel.Text = "Set Password";
                        cell.DetailTextLabel.Text = _settingsService.CurrentSettings.Password != null ? "Set" : "Not Set";
                        cell.AccessoryType = UITableViewCellAccessoryType.DisclosureIndicator;
                    }
                    break;

                case 1: // Sensitivity
                    cell.TextLabel.Text = "Motion Sensitivity";
                    cell.DetailTextLabel.Text = settings.SensitivityLevel.ToString();
                    cell.AccessoryType = UITableViewCellAccessoryType.DisclosureIndicator;
                    break;

                case 2: // Audio
                    if (indexPath.Row == 0)
                    {
                        cell.TextLabel.Text = "Volume";
                        var volumeSlider = new UISlider
                        {
                            MinValue = 0.0f,
                            MaxValue = 1.0f,
                            Value = settings.Volume
                        };
                        volumeSlider.ValueChanged += (s, e) =>
                        {
                            _settingsService.SetVolume(volumeSlider.Value);
                        };
                        cell.AccessoryView = volumeSlider;
                    }
                    else if (indexPath.Row == 1)
                    {
                        cell.TextLabel.Text = "Alarm Sound";
                        cell.DetailTextLabel.Text = Path.GetFileNameWithoutExtension(settings.AlarmSoundPath);
                        cell.AccessoryType = UITableViewCellAccessoryType.DisclosureIndicator;
                    }
                    else
                    {
                        cell.TextLabel.Text = "Max Duration";
                        cell.DetailTextLabel.Text = settings.MaxAlarmDuration == 0 ? "Unlimited" : $"{settings.MaxAlarmDuration}s";
                        cell.AccessoryType = UITableViewCellAccessoryType.DisclosureIndicator;
                    }
                    break;

                case 3: // Appearance
                    cell.TextLabel.Text = "Dark Mode";
                    var darkModeSwitch = new UISwitch { On = settings.DarkModeEnabled };
                    darkModeSwitch.ValueChanged += (s, e) =>
                    {
                        _settingsService.SetDarkMode(darkModeSwitch.On);
                    };
                    cell.AccessoryView = darkModeSwitch;
                    break;

                case 4: // Advanced
                    if (indexPath.Row == 0)
                    {
                        cell.TextLabel.Text = "Alarm Delay";
                        cell.DetailTextLabel.Text = $"{settings.AlarmDelay}s";
                        cell.AccessoryType = UITableViewCellAccessoryType.DisclosureIndicator;
                    }
                    else if (indexPath.Row == 1)
                    {
                        cell.TextLabel.Text = "Reset to Defaults";
                        cell.TextLabel.TextColor = UIColor.SystemRed;
                    }
                    else
                    {
                        cell.TextLabel.Text = "Export Data";
                        cell.AccessoryType = UITableViewCellAccessoryType.DisclosureIndicator;
                    }
                    break;
            }

            return cell;
        }

        public override void RowSelected(UITableView tableView, NSIndexPath indexPath)
        {
            tableView.DeselectRow(indexPath, true);

            switch (indexPath.Section)
            {
                case 0: // Security
                    if (indexPath.Row == 1)
                    {
                        ShowPasswordDialog();
                    }
                    break;

                case 1: // Sensitivity
                    ShowSensitivityPicker();
                    break;

                case 2: // Audio
                    if (indexPath.Row == 1)
                    {
                        ShowSoundPicker();
                    }
                    else if (indexPath.Row == 2)
                    {
                        ShowDurationPicker();
                    }
                    break;

                case 4: // Advanced
                    if (indexPath.Row == 0)
                    {
                        ShowDelayPicker();
                    }
                    else if (indexPath.Row == 1)
                    {
                        ShowResetConfirmation();
                    }
                    else if (indexPath.Row == 2)
                    {
                        ExportData();
                    }
                    break;
            }
        }

        private void ShowPasswordDialog()
        {
            var alert = UIAlertController.Create("Set Password", "Enter a password to disable the alarm", UIAlertControllerStyle.Alert);
            
            alert.AddTextField(textField =>
            {
                textField.Placeholder = "Password";
                textField.SecureTextEntry = true;
            });

            alert.AddAction(UIAlertAction.Create("Cancel", UIAlertActionStyle.Cancel, null));
            alert.AddAction(UIAlertAction.Create("Set", UIAlertActionStyle.Default, _ =>
            {
                var password = alert.TextFields[0].Text;
                if (!string.IsNullOrEmpty(password))
                {
                    var settings = _settingsService.CurrentSettings;
                    settings.Password = password;
                    _settingsService.UpdateSettings(settings);
                }
            }));

            GetViewController().PresentViewController(alert, true, null);
        }

        private void ShowSensitivityPicker()
        {
            var alert = UIAlertController.Create("Motion Sensitivity", "Select sensitivity level", UIAlertControllerStyle.ActionSheet);
            
            foreach (SensitivityLevel level in Enum.GetValues(typeof(SensitivityLevel)))
            {
                alert.AddAction(UIAlertAction.Create(level.ToString(), UIAlertActionStyle.Default, _ =>
                {
                    _settingsService.SetSensitivity(level);
                }));
            }

            alert.AddAction(UIAlertAction.Create("Cancel", UIAlertActionStyle.Cancel, null));
            GetViewController().PresentViewController(alert, true, null);
        }

        private void ShowSoundPicker()
        {
            var sounds = _settingsService.GetAvailableAlarmSounds();
            var alert = UIAlertController.Create("Alarm Sound", "Select alarm sound", UIAlertControllerStyle.ActionSheet);
            
            foreach (var sound in sounds)
            {
                alert.AddAction(UIAlertAction.Create(Path.GetFileNameWithoutExtension(sound), UIAlertActionStyle.Default, _ =>
                {
                    _settingsService.SetAlarmSound(sound);
                }));
            }

            alert.AddAction(UIAlertAction.Create("Cancel", UIAlertActionStyle.Cancel, null));
            GetViewController().PresentViewController(alert, true, null);
        }

        private void ShowDurationPicker()
        {
            var alert = UIAlertController.Create("Max Duration", "Select maximum alarm duration", UIAlertControllerStyle.ActionSheet);
            
            var durations = new[] { 0, 30, 60, 120, 300, 600 };
            foreach (var duration in durations)
            {
                var title = duration == 0 ? "Unlimited" : $"{duration} seconds";
                alert.AddAction(UIAlertAction.Create(title, UIAlertActionStyle.Default, _ =>
                {
                    _settingsService.SetMaxAlarmDuration(duration);
                }));
            }

            alert.AddAction(UIAlertAction.Create("Cancel", UIAlertActionStyle.Cancel, null));
            GetViewController().PresentViewController(alert, true, null);
        }

        private void ShowDelayPicker()
        {
            var alert = UIAlertController.Create("Alarm Delay", "Select delay before alarm triggers", UIAlertControllerStyle.ActionSheet);
            
            var delays = new[] { 0, 1, 3, 5, 10 };
            foreach (var delay in delays)
            {
                var title = delay == 0 ? "Immediate" : $"{delay} seconds";
                alert.AddAction(UIAlertAction.Create(title, UIAlertActionStyle.Default, _ =>
                {
                    _settingsService.SetAlarmDelay(delay);
                }));
            }

            alert.AddAction(UIAlertAction.Create("Cancel", UIAlertActionStyle.Cancel, null));
            GetViewController().PresentViewController(alert, true, null);
        }

        private void ShowResetConfirmation()
        {
            var alert = UIAlertController.Create("Reset Settings", "Are you sure you want to reset all settings to defaults?", UIAlertControllerStyle.Alert);
            
            alert.AddAction(UIAlertAction.Create("Cancel", UIAlertActionStyle.Cancel, null));
            alert.AddAction(UIAlertAction.Create("Reset", UIAlertActionStyle.Destructive, _ =>
            {
                _settingsService.ResetToDefaults();
            }));

            GetViewController().PresentViewController(alert, true, null);
        }

        private void ExportData()
        {
            var exportData = _settingsService.ExportData();
            if (exportData != null)
            {
                var activityController = new UIActivityViewController(new NSObject[] { NSString.FromData(NSData.FromString(exportData), NSStringEncoding.UTF8) }, null);
                GetViewController().PresentViewController(activityController, true, null);
            }
        }

        private UIViewController GetViewController()
        {
            // Helper method to get the current view controller
            var window = UIApplication.SharedApplication.KeyWindow;
            return window?.RootViewController?.PresentedViewController ?? window?.RootViewController;
        }
    }
}
