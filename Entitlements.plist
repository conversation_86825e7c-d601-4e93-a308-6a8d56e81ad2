<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<!-- Background App Refresh -->
	<key>com.apple.developer.background-processing</key>
	<true/>
	
	<!-- Background Audio -->
	<key>com.apple.developer.background-audio</key>
	<true/>
	
	<!-- Keychain Access -->
	<key>keychain-access-groups</key>
	<array>
		<string>$(AppIdentifierPrefix)com.antitheft.alarm</string>
	</array>
	
	<!-- App Groups (for sharing data between app and extensions) -->
	<key>com.apple.security.application-groups</key>
	<array>
		<string>group.com.antitheft.alarm</string>
	</array>
</dict>
</plist>
