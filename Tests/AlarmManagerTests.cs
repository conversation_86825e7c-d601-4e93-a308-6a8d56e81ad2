using System;
using System.Threading.Tasks;
using NUnit.Framework;
using AntiTheftAlarm.iOS.Models;
using AntiTheftAlarm.iOS.Services;

namespace AntiTheftAlarm.iOS.Tests
{
    /// <summary>
    /// Unit tests for AlarmManagerService
    /// </summary>
    [TestFixture]
    public class AlarmManagerTests
    {
        private AlarmManagerService _alarmManager;

        [SetUp]
        public void Setup()
        {
            _alarmManager = new AlarmManagerService();
        }

        [TearDown]
        public void TearDown()
        {
            _alarmManager?.Dispose();
        }

        [Test]
        public void AlarmManager_InitialState_ShouldBeInactive()
        {
            // Arrange & Act
            var state = _alarmManager.CurrentState;

            // Assert
            Assert.IsFalse(state.IsActive);
            Assert.IsFalse(state.IsTriggered);
            Assert.IsNull(state.TriggeredAlarmType);
        }

        [Test]
        public void AlarmManager_ActivateAlarm_ShouldSetActiveState()
        {
            // Arrange
            var eventFired = false;
            _alarmManager.AlarmStateChanged += (s, e) => eventFired = true;

            // Act
            _alarmManager.ActivateAlarm();

            // Assert
            Assert.IsTrue(_alarmManager.CurrentState.IsActive);
            Assert.IsNotNull(_alarmManager.CurrentState.ActivatedAt);
            Assert.IsTrue(eventFired);
        }

        [Test]
        public void AlarmManager_DeactivateAlarm_ShouldSetInactiveState()
        {
            // Arrange
            _alarmManager.ActivateAlarm();
            var eventFired = false;
            _alarmManager.AlarmStateChanged += (s, e) => eventFired = true;

            // Act
            _alarmManager.DeactivateAlarm();

            // Assert
            Assert.IsFalse(_alarmManager.CurrentState.IsActive);
            Assert.IsFalse(_alarmManager.CurrentState.IsTriggered);
            Assert.IsNull(_alarmManager.CurrentState.ActivatedAt);
            Assert.IsTrue(eventFired);
        }

        [Test]
        public void AlarmManager_EnableAlarmType_ShouldUpdateSettings()
        {
            // Arrange
            var alarmType = AlarmType.ChargerDisconnected;

            // Act
            _alarmManager.EnableAlarmType(alarmType, true);

            // Assert
            Assert.IsTrue(_alarmManager.Settings.EnabledAlarms[alarmType]);
        }

        [Test]
        public void AlarmManager_UpdateSensitivity_ShouldUpdateSettings()
        {
            // Arrange
            var newSensitivity = SensitivityLevel.High;

            // Act
            _alarmManager.UpdateSensitivity(newSensitivity);

            // Assert
            Assert.AreEqual(newSensitivity, _alarmManager.Settings.SensitivityLevel);
        }

        [Test]
        public void AlarmManager_GetAlarmHistory_ShouldReturnEmptyInitially()
        {
            // Act
            var history = _alarmManager.GetAlarmHistory();

            // Assert
            Assert.IsNotNull(history);
            Assert.AreEqual(0, history.Count);
        }

        [Test]
        public void AlarmManager_ClearAlarmHistory_ShouldRemoveAllEvents()
        {
            // Arrange
            // Simulate some alarm events (this would normally come from actual triggers)
            
            // Act
            _alarmManager.ClearAlarmHistory();
            var history = _alarmManager.GetAlarmHistory();

            // Assert
            Assert.AreEqual(0, history.Count);
        }
    }

    /// <summary>
    /// Unit tests for AlarmSettings
    /// </summary>
    [TestFixture]
    public class AlarmSettingsTests
    {
        [Test]
        public void AlarmSettings_DefaultConstructor_ShouldSetDefaults()
        {
            // Act
            var settings = new AlarmSettings();

            // Assert
            Assert.AreEqual("default_alarm.mp3", settings.AlarmSoundPath);
            Assert.AreEqual(1.0f, settings.Volume);
            Assert.IsTrue(settings.RequireAuthentication);
            Assert.IsTrue(settings.UseVibration);
            Assert.AreEqual(SensitivityLevel.Medium, settings.SensitivityLevel);
            Assert.AreEqual(3, settings.AlarmDelay);
            Assert.AreEqual(0, settings.MaxAlarmDuration);

            // Check that all alarm types are disabled by default
            foreach (AlarmType alarmType in Enum.GetValues(typeof(AlarmType)))
            {
                Assert.IsFalse(settings.EnabledAlarms[alarmType]);
            }
        }
    }

    /// <summary>
    /// Unit tests for MotionData
    /// </summary>
    [TestFixture]
    public class MotionDataTests
    {
        [Test]
        public void MotionData_AccelerationMagnitude_ShouldCalculateCorrectly()
        {
            // Arrange
            var motionData = new MotionData
            {
                AccelerationX = 3.0,
                AccelerationY = 4.0,
                AccelerationZ = 0.0
            };

            // Act
            var magnitude = motionData.AccelerationMagnitude;

            // Assert
            Assert.AreEqual(5.0, magnitude, 0.001); // 3-4-5 triangle
        }

        [Test]
        public void MotionData_RotationMagnitude_ShouldCalculateCorrectly()
        {
            // Arrange
            var motionData = new MotionData
            {
                RotationX = 1.0,
                RotationY = 1.0,
                RotationZ = 1.0
            };

            // Act
            var magnitude = motionData.RotationMagnitude;

            // Assert
            Assert.AreEqual(Math.Sqrt(3), magnitude, 0.001);
        }
    }

    /// <summary>
    /// Integration tests for the alarm system
    /// </summary>
    [TestFixture]
    public class AlarmSystemIntegrationTests
    {
        private AlarmManagerService _alarmManager;
        private SettingsService _settingsService;

        [SetUp]
        public void Setup()
        {
            _alarmManager = new AlarmManagerService();
            _settingsService = new SettingsService();
        }

        [TearDown]
        public void TearDown()
        {
            _alarmManager?.Dispose();
        }

        [Test]
        public void AlarmSystem_FullWorkflow_ShouldWorkCorrectly()
        {
            // Arrange
            var alarmTriggered = false;
            var stateChanged = false;

            _alarmManager.AlarmTriggered += (s, e) => alarmTriggered = true;
            _alarmManager.AlarmStateChanged += (s, e) => stateChanged = true;

            // Act - Enable alarm type
            _alarmManager.EnableAlarmType(AlarmType.DeviceMovement, true);
            
            // Act - Activate alarm
            _alarmManager.ActivateAlarm();

            // Assert
            Assert.IsTrue(_alarmManager.CurrentState.IsActive);
            Assert.IsTrue(stateChanged);
            Assert.IsTrue(_alarmManager.Settings.EnabledAlarms[AlarmType.DeviceMovement]);

            // Act - Deactivate alarm
            _alarmManager.DeactivateAlarm();

            // Assert
            Assert.IsFalse(_alarmManager.CurrentState.IsActive);
        }

        [Test]
        public void SettingsService_SaveAndLoad_ShouldPersistData()
        {
            // Arrange
            var originalSettings = _settingsService.CurrentSettings;
            originalSettings.Volume = 0.8f;
            originalSettings.SensitivityLevel = SensitivityLevel.High;
            originalSettings.DarkModeEnabled = true;

            // Act
            _settingsService.UpdateSettings(originalSettings);
            _settingsService.LoadSettings();

            // Assert
            var loadedSettings = _settingsService.CurrentSettings;
            Assert.AreEqual(0.8f, loadedSettings.Volume);
            Assert.AreEqual(SensitivityLevel.High, loadedSettings.SensitivityLevel);
            Assert.IsTrue(loadedSettings.DarkModeEnabled);
        }
    }
}
