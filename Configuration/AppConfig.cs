using System;

namespace AntiTheftAlarm.iOS.Configuration
{
    /// <summary>
    /// Application configuration constants and settings
    /// </summary>
    public static class AppConfig
    {
        // App Information
        public const string AppName = "Anti-Theft Alarm";
        public const string AppVersion = "1.0.0";
        public const string AppBundleId = "com.antitheft.alarm";
        
        // Background Task Identifiers
        public const string BackgroundTaskIdentifier = "com.antitheft.alarm.monitoring";
        public const string BackgroundRefreshIdentifier = "com.antitheft.alarm.refresh";
        
        // Keychain Configuration
        public const string KeychainService = "AntiTheftAlarm";
        public const string PasswordKeychainKey = "AntiTheftAlarm_Password";
        
        // File Names
        public const string SettingsFileName = "alarm_settings.json";
        public const string HistoryFileName = "alarm_history.json";
        public const string DefaultAlarmSound = "default_alarm.mp3";
        
        // Timing Configuration (in seconds)
        public const double DefaultBackgroundTaskInterval = 30.0;
        public const double VolumeMonitoringInterval = 0.5;
        public const double VibrationInterval = 1.0;
        public const int MaxAlarmHistoryCount = 100;
        
        // Motion Detection Thresholds
        public static class MotionThresholds
        {
            // Acceleration thresholds (g-force)
            public const double LowAcceleration = 0.5;
            public const double MediumAcceleration = 0.3;
            public const double HighAcceleration = 0.2;
            public const double VeryHighAcceleration = 0.1;
            
            // Rotation thresholds (rad/s)
            public const double LowRotation = 1.0;
            public const double MediumRotation = 0.7;
            public const double HighRotation = 0.5;
            public const double VeryHighRotation = 0.3;
        }
        
        // Update Intervals for Motion Monitoring
        public static class MotionUpdateIntervals
        {
            public const double Low = 0.5;
            public const double Medium = 0.2;
            public const double High = 0.1;
            public const double VeryHigh = 0.05;
        }
        
        // Check Intervals for Motion Monitoring
        public static class MotionCheckIntervals
        {
            public const double Low = 1.0;
            public const double Medium = 0.5;
            public const double High = 0.3;
            public const double VeryHigh = 0.1;
        }
        
        // Audio Configuration
        public static class Audio
        {
            public const float DefaultVolume = 1.0f;
            public const float MinVolume = 0.0f;
            public const float MaxVolume = 1.0f;
            public const float LowVolumeThreshold = 0.8f;
        }
        
        // UI Configuration
        public static class UI
        {
            public const float CornerRadius = 12.0f;
            public const float ButtonCornerRadius = 25.0f;
            public const float StackViewSpacing = 16.0f;
            public const float StandardMargin = 20.0f;
            public const float SmallMargin = 10.0f;
            public const float ButtonHeight = 50.0f;
            public const float CellHeight = 50.0f;
        }
        
        // Notification Configuration
        public static class Notifications
        {
            public const string AlarmTriggeredId = "alarm_triggered";
            public const string AlarmActivatedId = "alarm_activated";
            public const string AlarmDeactivatedId = "alarm_deactivated";
            public const string ReminderPrefix = "reminder_";
        }
        
        // Security Configuration
        public static class Security
        {
            public const int MinPasswordLength = 4;
            public const int MaxPasswordLength = 20;
            public const string BiometricPrompt = "Authenticate to disable the anti-theft alarm";
            public const string FallbackTitle = "Use Password";
        }
        
        // Feature Flags
        public static class Features
        {
            public const bool EnableLocationTracking = false;
            public const bool EnableCloudSync = false;
            public const bool EnableAdvancedLogging = true;
            public const bool EnableCrashReporting = false;
        }
        
        // Debug Configuration
        public static class Debug
        {
            #if DEBUG
            public const bool IsDebugMode = true;
            public const bool EnableVerboseLogging = true;
            public const bool EnableTestMode = true;
            #else
            public const bool IsDebugMode = false;
            public const bool EnableVerboseLogging = false;
            public const bool EnableTestMode = false;
            #endif
        }
        
        // URLs and Links
        public static class URLs
        {
            public const string SupportEmail = "<EMAIL>";
            public const string PrivacyPolicy = "https://antitheftalarm.com/privacy";
            public const string TermsOfService = "https://antitheftalarm.com/terms";
            public const string AppStore = "https://apps.apple.com/app/anti-theft-alarm";
        }
        
        // Error Messages
        public static class ErrorMessages
        {
            public const string MotionSensorsNotAvailable = "Motion sensors are not available on this device";
            public const string BiometricNotAvailable = "Biometric authentication is not available";
            public const string BiometricNotEnrolled = "No biometric data enrolled";
            public const string BiometricLockout = "Biometric authentication is locked out";
            public const string AudioSessionFailed = "Failed to configure audio session";
            public const string BackgroundTaskFailed = "Failed to start background task";
            public const string PermissionDenied = "Required permission was denied";
            public const string InvalidPassword = "Invalid password";
            public const string PasswordTooShort = "Password is too short";
            public const string PasswordTooLong = "Password is too long";
        }
        
        // Success Messages
        public static class SuccessMessages
        {
            public const string AlarmActivated = "Anti-theft alarm activated successfully";
            public const string AlarmDeactivated = "Anti-theft alarm deactivated";
            public const string SettingsSaved = "Settings saved successfully";
            public const string PasswordSet = "Password set successfully";
            public const string BiometricEnabled = "Biometric authentication enabled";
            public const string SoundImported = "Custom alarm sound imported";
        }
        
        // Helper Methods
        public static double GetMotionThreshold(Models.SensitivityLevel sensitivity, bool isAcceleration)
        {
            if (isAcceleration)
            {
                return sensitivity switch
                {
                    Models.SensitivityLevel.Low => MotionThresholds.LowAcceleration,
                    Models.SensitivityLevel.Medium => MotionThresholds.MediumAcceleration,
                    Models.SensitivityLevel.High => MotionThresholds.HighAcceleration,
                    Models.SensitivityLevel.VeryHigh => MotionThresholds.VeryHighAcceleration,
                    _ => MotionThresholds.MediumAcceleration
                };
            }
            else
            {
                return sensitivity switch
                {
                    Models.SensitivityLevel.Low => MotionThresholds.LowRotation,
                    Models.SensitivityLevel.Medium => MotionThresholds.MediumRotation,
                    Models.SensitivityLevel.High => MotionThresholds.HighRotation,
                    Models.SensitivityLevel.VeryHigh => MotionThresholds.VeryHighRotation,
                    _ => MotionThresholds.MediumRotation
                };
            }
        }
        
        public static double GetUpdateInterval(Models.SensitivityLevel sensitivity)
        {
            return sensitivity switch
            {
                Models.SensitivityLevel.Low => MotionUpdateIntervals.Low,
                Models.SensitivityLevel.Medium => MotionUpdateIntervals.Medium,
                Models.SensitivityLevel.High => MotionUpdateIntervals.High,
                Models.SensitivityLevel.VeryHigh => MotionUpdateIntervals.VeryHigh,
                _ => MotionUpdateIntervals.Medium
            };
        }
        
        public static double GetCheckInterval(Models.SensitivityLevel sensitivity)
        {
            return sensitivity switch
            {
                Models.SensitivityLevel.Low => MotionCheckIntervals.Low,
                Models.SensitivityLevel.Medium => MotionCheckIntervals.Medium,
                Models.SensitivityLevel.High => MotionCheckIntervals.High,
                Models.SensitivityLevel.VeryHigh => MotionCheckIntervals.VeryHigh,
                _ => MotionCheckIntervals.Medium
            };
        }
    }
}
