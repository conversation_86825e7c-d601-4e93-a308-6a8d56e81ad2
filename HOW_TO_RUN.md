# 📱 كيفية تشغيل تطبيق Anti-Theft Alarm على iPhone

## 🚀 الطريقة السريعة (باستخدام Xcode)

### 1. فتح المشروع
```bash
# انتقل إلى مجلد المشروع
cd /Users/<USER>/Projects/antitheft

# افتح مشروع Xcode
open AntiTheftDemo.xcodeproj
```

### 2. إعداد المشروع في Xcode
1. **اختر الهدف**: في Xcode، اختر جهازك من قائمة الأهداف (أو iPhone Simulator)
2. **تعيين Team**: اذهب إلى Project Settings > Signing & Capabilities > Team واختر حساب Apple Developer الخاص بك
3. **تغيير Bundle Identifier**: غير `com.antitheft.demo` إلى معرف فريد مثل `com.yourname.antitheft`

### 3. تشغيل التطبيق
- اضغط ▶️ (Run) أو `Cmd + R`
- سيتم بناء التطبيق وتشغيله على جهازك

## 📋 متطلبات التشغيل

### للجهاز الحقيقي:
- ✅ حساب Apple Developer (مجاني أو مدفوع)
- ✅ كابل USB لتوصيل iPhone بالكمبيوتر
- ✅ تفعيل "Developer Mode" على iPhone
- ✅ الثقة في الكمبيوتر على iPhone

### للمحاكي:
- ✅ Xcode مع iOS Simulator
- ⚠️ بعض الميزات محدودة (مثل مستشعرات الحركة الحقيقية)

## 🔧 إعداد iPhone للتطوير

### 1. تفعيل Developer Mode
```
iPhone Settings > Privacy & Security > Developer Mode > تفعيل
```

### 2. الثقة في التطبيق
بعد تثبيت التطبيق:
```
iPhone Settings > General > VPN & Device Management > 
اختر حساب Developer > Trust
```

## 🧪 كيفية اختبار الميزات

### 1. تفعيل الإنذار
- افتح التطبيق
- تأكد من تفعيل "📱 إنذار الحركة" (مفعل افتراضياً)
- اضغط "تفعيل الإنذار"
- ستتغير الحالة إلى "🛡️ الإنذار مفعل - جاري المراقبة..."

### 2. اختبار إنذار الحركة
- بعد تفعيل الإنذار، انتظر 3 ثوانٍ
- حرك الجهاز بقوة أو هزه
- سيتم تفعيل الإنذار مع:
  - 🔊 صوت إنذار عالي
  - 📳 اهتزاز قوي
  - 🚨 رسالة تنبيه

### 3. إيقاف الإنذار
- عند ظهور رسالة الإنذار، اضغط "إيقاف الإنذار"
- سيطلب منك المصادقة:
  - Face ID/Touch ID (إذا متاح)
  - أو رمز المرور
- بعد المصادقة الناجحة، سيتم إيقاف الإنذار

### 4. اختبار الميزات الأخرى
- **🔌 إنذار الشاحن**: فعل المفتاح وافصل الشاحن (محاكاة بعد 5 ثوانٍ)
- **👖 إنذار الجيب**: فعل المفتاح واختبر الحركة السريعة
- **🎧 إنذار السماعات**: فعل المفتاح وافصل السماعات

## 🔍 استكشاف الأخطاء

### إذا لم يعمل التطبيق:

#### 1. مشاكل البناء
```bash
# تنظيف المشروع
Product > Clean Build Folder (Cmd + Shift + K)

# إعادة البناء
Product > Build (Cmd + B)
```

#### 2. مشاكل الصلاحيات
- تأكد من منح صلاحيات الحركة عند السؤال
- اذهب إلى Settings > Privacy & Security > Motion & Fitness > Anti-Theft Alarm

#### 3. مشاكل المصادقة
- تأكد من تفعيل Face ID/Touch ID في الإعدادات
- أو استخدم رمز المرور

#### 4. مشاكل الصوت
- تأكد من عدم تفعيل الوضع الصامت
- ارفع مستوى الصوت
- تأكد من عدم توصيل سماعات

## 📊 مراقبة الأداء

### في Xcode Console:
ستجد رسائل مفيدة مثل:
```
🚀 تم تشغيل تطبيق Anti-Theft Alarm
✅ تم تكوين جلسة الصوت
📱 تم تحميل الواجهة الرئيسية
✅ مستشعر التسارع متاح
🔒 تم تفعيل نظام الإنذار
🔍 بدأت مراقبة الحركة
📊 تم تسجيل القراءة الأساسية: 1.023
🚨 تم تفعيل الإنذار: اكتشاف حركة
```

## 🎯 نصائح للاختبار الأمثل

### 1. على الجهاز الحقيقي:
- ✅ جميع المستشعرات تعمل بكفاءة
- ✅ Face ID/Touch ID يعمل
- ✅ الاهتزاز والصوت واضحان
- ✅ العمل في الخلفية محدود

### 2. على المحاكي:
- ⚠️ محاكاة الحركة محدودة
- ❌ لا يوجد Face ID/Touch ID
- ❌ لا يوجد اهتزاز
- ✅ الواجهة والتنقل يعملان

### 3. اختبار شامل:
1. فعل كل نوع إنذار على حدة
2. اختبر في حالات مختلفة (شاحن متصل/منفصل)
3. اختبر المصادقة بطرق مختلفة
4. اختبر الإعدادات والسجل

## 🆘 إذا احتجت مساعدة

### مشاكل شائعة:
1. **"Developer Mode not enabled"**: فعل Developer Mode في إعدادات iPhone
2. **"Untrusted Developer"**: اذهب إلى Settings > General > VPN & Device Management
3. **"Motion permission denied"**: اذهب إلى Settings > Privacy > Motion & Fitness
4. **"No sound"**: تحقق من مستوى الصوت والوضع الصامت

### للحصول على مساعدة:
- راجع رسائل الخطأ في Xcode Console
- تأكد من تحديث Xcode وiOS
- جرب على جهاز مختلف إذا أمكن

---

## 🎉 استمتع بتجربة التطبيق!

التطبيق جاهز للاختبار الكامل على جهاز iPhone الخاص بك. جرب جميع الميزات واستكشف كيف يعمل نظام الحماية من السرقة!
