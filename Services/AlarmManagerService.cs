using System;
using System.Collections.Generic;
using System.Linq;
using AntiTheftAlarm.iOS.Models;

namespace AntiTheftAlarm.iOS.Services
{
    /// <summary>
    /// Main service for managing all alarm monitoring services
    /// </summary>
    public class AlarmManagerService
    {
        private readonly Dictionary<AlarmType, IAlarmMonitoringService> _monitoringServices;
        private readonly List<AlarmEvent> _alarmHistory;
        private AlarmSettings _settings;
        private AlarmState _currentState;

        public event EventHandler<AlarmTriggeredEventArgs> AlarmTriggered;
        public event EventHandler<AlarmStateChangedEventArgs> AlarmStateChanged;

        public AlarmManagerService()
        {
            _monitoringServices = new Dictionary<AlarmType, IAlarmMonitoringService>();
            _alarmHistory = new List<AlarmEvent>();
            _settings = new AlarmSettings();
            _currentState = new AlarmState();

            InitializeMonitoringServices();
        }

        public AlarmSettings Settings
        {
            get => _settings;
            set
            {
                _settings = value;
                UpdateMonitoringServices();
            }
        }

        public AlarmState CurrentState => _currentState;

        public IReadOnlyList<AlarmEvent> AlarmHistory => _alarmHistory.AsReadOnly();

        private void InitializeMonitoringServices()
        {
            // Initialize all monitoring services
            var chargerService = new ChargerMonitoringService();
            var motionService = new MotionMonitoringService();
            var headphonesService = new HeadphonesMonitoringService();

            // Map services to alarm types
            _monitoringServices[AlarmType.ChargerDisconnected] = chargerService;
            _monitoringServices[AlarmType.DeviceMovement] = motionService;
            _monitoringServices[AlarmType.PocketRemoval] = motionService; // Same service handles both
            _monitoringServices[AlarmType.HeadphonesDisconnected] = headphonesService;

            // Subscribe to alarm events
            foreach (var service in _monitoringServices.Values.Distinct())
            {
                service.AlarmTriggered += OnAlarmTriggered;
            }
        }

        public void ActivateAlarm()
        {
            if (_currentState.IsActive)
                return;

            _currentState.IsActive = true;
            _currentState.ActivatedAt = DateTime.Now;
            _currentState.LastUpdated = DateTime.Now;

            // Start monitoring for enabled alarm types
            foreach (var alarmType in _settings.EnabledAlarms.Where(kvp => kvp.Value).Select(kvp => kvp.Key))
            {
                if (_monitoringServices.TryGetValue(alarmType, out var service))
                {
                    service.UpdateSensitivity(_settings.SensitivityLevel);
                    service.StartMonitoring(alarmType);
                }
            }

            OnAlarmStateChanged(new AlarmStateChangedEventArgs(_currentState, "Alarm activated"));
            Console.WriteLine("Alarm system activated");
        }

        public void DeactivateAlarm()
        {
            if (!_currentState.IsActive)
                return;

            // Stop all monitoring
            foreach (var service in _monitoringServices.Values.Distinct())
            {
                service.StopAllMonitoring();
            }

            _currentState.IsActive = false;
            _currentState.IsTriggered = false;
            _currentState.TriggeredAlarmType = null;
            _currentState.ActivatedAt = null;
            _currentState.TriggeredAt = null;
            _currentState.LastUpdated = DateTime.Now;

            OnAlarmStateChanged(new AlarmStateChangedEventArgs(_currentState, "Alarm deactivated"));
            Console.WriteLine("Alarm system deactivated");
        }

        public void EnableAlarmType(AlarmType alarmType, bool enabled)
        {
            _settings.EnabledAlarms[alarmType] = enabled;

            if (_currentState.IsActive)
            {
                if (enabled && _monitoringServices.TryGetValue(alarmType, out var service))
                {
                    service.UpdateSensitivity(_settings.SensitivityLevel);
                    service.StartMonitoring(alarmType);
                }
                else if (!enabled && _monitoringServices.TryGetValue(alarmType, out service))
                {
                    service.StopMonitoring(alarmType);
                }
            }

            Console.WriteLine($"Alarm type {alarmType} {(enabled ? "enabled" : "disabled")}");
        }

        public void UpdateSensitivity(SensitivityLevel sensitivity)
        {
            _settings.SensitivityLevel = sensitivity;

            // Update all monitoring services
            foreach (var service in _monitoringServices.Values.Distinct())
            {
                service.UpdateSensitivity(sensitivity);
            }
        }

        public void ClearAlarmHistory()
        {
            _alarmHistory.Clear();
        }

        public List<AlarmEvent> GetAlarmHistory(DateTime? fromDate = null, AlarmType? alarmType = null)
        {
            var query = _alarmHistory.AsEnumerable();

            if (fromDate.HasValue)
            {
                query = query.Where(e => e.Timestamp >= fromDate.Value);
            }

            if (alarmType.HasValue)
            {
                query = query.Where(e => e.AlarmType == alarmType.Value);
            }

            return query.OrderByDescending(e => e.Timestamp).ToList();
        }

        private void OnAlarmTriggered(object sender, AlarmTriggeredEventArgs e)
        {
            if (!_currentState.IsActive)
                return;

            // Update current state
            _currentState.IsTriggered = true;
            _currentState.TriggeredAlarmType = e.AlarmType;
            _currentState.TriggeredAt = e.Timestamp;
            _currentState.LastUpdated = DateTime.Now;

            // Create alarm event
            var alarmEvent = new AlarmEvent
            {
                AlarmType = e.AlarmType,
                Timestamp = e.Timestamp,
                Details = e.Details
            };

            _alarmHistory.Add(alarmEvent);

            // Forward the event
            AlarmTriggered?.Invoke(this, e);
            OnAlarmStateChanged(new AlarmStateChangedEventArgs(_currentState, $"Alarm triggered: {e.AlarmType}"));

            Console.WriteLine($"Alarm manager: {e.AlarmType} triggered at {e.Timestamp}");
        }

        private void OnAlarmStateChanged(AlarmStateChangedEventArgs e)
        {
            AlarmStateChanged?.Invoke(this, e);
        }

        private void UpdateMonitoringServices()
        {
            if (!_currentState.IsActive)
                return;

            // Update sensitivity for all services
            foreach (var service in _monitoringServices.Values.Distinct())
            {
                service.UpdateSensitivity(_settings.SensitivityLevel);
            }
        }

        public void Dispose()
        {
            DeactivateAlarm();
            
            foreach (var service in _monitoringServices.Values.Distinct())
            {
                if (service is IDisposable disposable)
                {
                    disposable.Dispose();
                }
            }
        }
    }

    /// <summary>
    /// Event arguments for alarm state changes
    /// </summary>
    public class AlarmStateChangedEventArgs : EventArgs
    {
        public AlarmStateChangedEventArgs(AlarmState state, string message)
        {
            State = state;
            Message = message;
            Timestamp = DateTime.Now;
        }

        public AlarmState State { get; }
        public string Message { get; }
        public DateTime Timestamp { get; }
    }
}
