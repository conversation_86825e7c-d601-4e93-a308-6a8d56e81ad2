using System;
using Foundation;
using UserNotifications;
using UIKit;

namespace AntiTheftAlarm.iOS.Services
{
    /// <summary>
    /// Service for managing local notifications
    /// </summary>
    public class NotificationService
    {
        private UNUserNotificationCenter _notificationCenter;

        public NotificationService()
        {
            _notificationCenter = UNUserNotificationCenter.Current;
            RequestPermissions();
        }

        private async void RequestPermissions()
        {
            var (granted, error) = await _notificationCenter.RequestAuthorizationAsync(
                UNAuthorizationOptions.Alert | UNAuthorizationOptions.Sound | UNAuthorizationOptions.Badge);

            if (!granted)
            {
                Console.WriteLine("Notification permissions not granted");
            }
        }

        /// <summary>
        /// Send immediate notification for alarm trigger
        /// </summary>
        public void SendAlarmTriggeredNotification(string alarmType, string details)
        {
            var content = new UNMutableNotificationContent
            {
                Title = "🚨 ALARM TRIGGERED!",
                Body = $"{alarmType} detected. {details}",
                Sound = UNNotificationSound.Default,
                Badge = 1
            };

            var request = UNNotificationRequest.FromIdentifier(
                Guid.NewGuid().ToString(),
                content,
                UNTimeIntervalNotificationTrigger.CreateTrigger(0.1, false));

            _notificationCenter.AddNotificationRequest(request, error =>
            {
                if (error != null)
                {
                    Console.WriteLine($"Error sending notification: {error.LocalizedDescription}");
                }
            });
        }

        /// <summary>
        /// Send notification when alarm is activated
        /// </summary>
        public void SendAlarmActivatedNotification()
        {
            var content = new UNMutableNotificationContent
            {
                Title = "🛡️ Anti-Theft Alarm Active",
                Body = "Your device is now protected. Any unauthorized access will trigger the alarm.",
                Sound = UNNotificationSound.Default
            };

            var request = UNNotificationRequest.FromIdentifier(
                "alarm_activated",
                content,
                UNTimeIntervalNotificationTrigger.CreateTrigger(1.0, false));

            _notificationCenter.AddNotificationRequest(request, null);
        }

        /// <summary>
        /// Send notification when alarm is deactivated
        /// </summary>
        public void SendAlarmDeactivatedNotification()
        {
            var content = new UNMutableNotificationContent
            {
                Title = "✅ Anti-Theft Alarm Deactivated",
                Body = "Your device protection has been disabled.",
                Sound = UNNotificationSound.Default
            };

            var request = UNNotificationRequest.FromIdentifier(
                "alarm_deactivated",
                content,
                UNTimeIntervalNotificationTrigger.CreateTrigger(0.5, false));

            _notificationCenter.AddNotificationRequest(request, null);
        }

        /// <summary>
        /// Schedule reminder notification
        /// </summary>
        public void ScheduleReminderNotification(double timeInterval, string message)
        {
            var content = new UNMutableNotificationContent
            {
                Title = "🔔 Anti-Theft Reminder",
                Body = message,
                Sound = UNNotificationSound.Default
            };

            var request = UNNotificationRequest.FromIdentifier(
                $"reminder_{DateTime.Now.Ticks}",
                content,
                UNTimeIntervalNotificationTrigger.CreateTrigger(timeInterval, false));

            _notificationCenter.AddNotificationRequest(request, null);
        }

        /// <summary>
        /// Clear all notifications
        /// </summary>
        public void ClearAllNotifications()
        {
            _notificationCenter.RemoveAllDeliveredNotifications();
            _notificationCenter.RemoveAllPendingNotificationRequests();
        }

        /// <summary>
        /// Clear badge count
        /// </summary>
        public void ClearBadge()
        {
            UIApplication.SharedApplication.ApplicationIconBadgeNumber = 0;
        }
    }
}
