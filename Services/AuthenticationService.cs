using System;
using System.Threading.Tasks;
using Foundation;
using LocalAuthentication;
using Security;
using AntiTheftAlarm.iOS.Models;

namespace AntiTheftAlarm.iOS.Services
{
    /// <summary>
    /// Service for handling biometric and password authentication
    /// </summary>
    public class AuthenticationService
    {
        private const string PasswordKeychainKey = "AntiTheftAlarm_Password";
        private const string KeychainService = "AntiTheftAlarm";

        public event EventHandler<AuthenticationEventArgs> AuthenticationCompleted;

        /// <summary>
        /// Check if biometric authentication is available
        /// </summary>
        public async Task<BiometricAuthenticationStatus> GetBiometricAuthenticationStatusAsync()
        {
            var context = new LAContext();
            var error = new Foundation.NSError();

            var result = context.CanEvaluatePolicy(LAPolicy.DeviceOwnerAuthenticationWithBiometrics, out error);

            if (!result)
            {
                if (error != null)
                {
                    return error.Code switch
                    {
                        (int)LAError.BiometryNotAvailable => BiometricAuthenticationStatus.NotAvailable,
                        (int)LAError.BiometryNotEnrolled => BiometricAuthenticationStatus.NotEnrolled,
                        (int)LAError.BiometryLockout => BiometricAuthenticationStatus.LockedOut,
                        _ => BiometricAuthenticationStatus.NotAvailable
                    };
                }
                return BiometricAuthenticationStatus.NotAvailable;
            }

            // Determine the type of biometric authentication
            if (context.BiometryType == LABiometryType.FaceId)
            {
                return BiometricAuthenticationStatus.FaceIDAvailable;
            }
            else if (context.BiometryType == LABiometryType.TouchId)
            {
                return BiometricAuthenticationStatus.TouchIDAvailable;
            }

            return BiometricAuthenticationStatus.Available;
        }

        /// <summary>
        /// Authenticate using biometric authentication
        /// </summary>
        public async Task<AuthenticationResult> AuthenticateWithBiometricsAsync(string reason = "Authenticate to disable the anti-theft alarm")
        {
            try
            {
                var context = new LAContext();
                context.LocalizedFallbackTitle = "Use Password";

                var result = await context.EvaluatePolicyAsync(
                    LAPolicy.DeviceOwnerAuthenticationWithBiometrics,
                    reason);

                var authResult = new AuthenticationResult
                {
                    IsSuccessful = result.Item1,
                    Method = DisableMethod.FaceID, // Will be updated based on actual biometry type
                    ErrorMessage = result.Item2?.LocalizedDescription
                };

                // Determine the actual method used
                if (result.Item1)
                {
                    authResult.Method = context.BiometryType == LABiometryType.FaceId 
                        ? DisableMethod.FaceID 
                        : DisableMethod.TouchID;
                }

                AuthenticationCompleted?.Invoke(this, new AuthenticationEventArgs(authResult));
                return authResult;
            }
            catch (Exception ex)
            {
                var authResult = new AuthenticationResult
                {
                    IsSuccessful = false,
                    ErrorMessage = ex.Message
                };

                AuthenticationCompleted?.Invoke(this, new AuthenticationEventArgs(authResult));
                return authResult;
            }
        }

        /// <summary>
        /// Authenticate using device passcode
        /// </summary>
        public async Task<AuthenticationResult> AuthenticateWithDevicePasscodeAsync(string reason = "Authenticate to disable the anti-theft alarm")
        {
            try
            {
                var context = new LAContext();
                
                var result = await context.EvaluatePolicyAsync(
                    LAPolicy.DeviceOwnerAuthentication,
                    reason);

                var authResult = new AuthenticationResult
                {
                    IsSuccessful = result.Item1,
                    Method = DisableMethod.Password,
                    ErrorMessage = result.Item2?.LocalizedDescription
                };

                AuthenticationCompleted?.Invoke(this, new AuthenticationEventArgs(authResult));
                return authResult;
            }
            catch (Exception ex)
            {
                var authResult = new AuthenticationResult
                {
                    IsSuccessful = false,
                    ErrorMessage = ex.Message
                };

                AuthenticationCompleted?.Invoke(this, new AuthenticationEventArgs(authResult));
                return authResult;
            }
        }

        /// <summary>
        /// Set a custom password for the app
        /// </summary>
        public bool SetCustomPassword(string password)
        {
            try
            {
                var passwordData = NSData.FromString(password);
                var record = new SecRecord(SecKind.GenericPassword)
                {
                    Service = KeychainService,
                    Account = PasswordKeychainKey,
                    ValueData = passwordData
                };

                // Delete existing password if any
                SecKeyChain.Remove(record);

                // Add new password
                var result = SecKeyChain.Add(record);
                return result == SecStatusCode.Success;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to set custom password: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Verify custom password
        /// </summary>
        public AuthenticationResult VerifyCustomPassword(string password)
        {
            try
            {
                var query = new SecRecord(SecKind.GenericPassword)
                {
                    Service = KeychainService,
                    Account = PasswordKeychainKey
                };

                var result = SecKeyChain.QueryAsData(query);
                if (result.Item1 == SecStatusCode.Success && result.Item2 != null)
                {
                    var storedPassword = result.Item2.ToString();
                    var isValid = storedPassword == password;

                    var authResult = new AuthenticationResult
                    {
                        IsSuccessful = isValid,
                        Method = DisableMethod.Password,
                        ErrorMessage = isValid ? null : "Invalid password"
                    };

                    AuthenticationCompleted?.Invoke(this, new AuthenticationEventArgs(authResult));
                    return authResult;
                }

                var failResult = new AuthenticationResult
                {
                    IsSuccessful = false,
                    Method = DisableMethod.Password,
                    ErrorMessage = "No password set"
                };

                AuthenticationCompleted?.Invoke(this, new AuthenticationEventArgs(failResult));
                return failResult;
            }
            catch (Exception ex)
            {
                var authResult = new AuthenticationResult
                {
                    IsSuccessful = false,
                    Method = DisableMethod.Password,
                    ErrorMessage = ex.Message
                };

                AuthenticationCompleted?.Invoke(this, new AuthenticationEventArgs(authResult));
                return authResult;
            }
        }

        /// <summary>
        /// Check if custom password is set
        /// </summary>
        public bool HasCustomPassword()
        {
            try
            {
                var query = new SecRecord(SecKind.GenericPassword)
                {
                    Service = KeychainService,
                    Account = PasswordKeychainKey
                };

                var result = SecKeyChain.QueryAsData(query);
                return result.Item1 == SecStatusCode.Success;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Remove custom password
        /// </summary>
        public bool RemoveCustomPassword()
        {
            try
            {
                var record = new SecRecord(SecKind.GenericPassword)
                {
                    Service = KeychainService,
                    Account = PasswordKeychainKey
                };

                var result = SecKeyChain.Remove(record);
                return result == SecStatusCode.Success;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to remove custom password: {ex.Message}");
                return false;
            }
        }
    }

    /// <summary>
    /// Authentication result
    /// </summary>
    public class AuthenticationResult
    {
        public bool IsSuccessful { get; set; }
        public DisableMethod? Method { get; set; }
        public string ErrorMessage { get; set; }
    }

    /// <summary>
    /// Biometric authentication status
    /// </summary>
    public enum BiometricAuthenticationStatus
    {
        NotAvailable,
        NotEnrolled,
        LockedOut,
        Available,
        FaceIDAvailable,
        TouchIDAvailable
    }

    /// <summary>
    /// Event arguments for authentication events
    /// </summary>
    public class AuthenticationEventArgs : EventArgs
    {
        public AuthenticationEventArgs(AuthenticationResult result)
        {
            Result = result;
            Timestamp = DateTime.Now;
        }

        public AuthenticationResult Result { get; }
        public DateTime Timestamp { get; }
    }
}
