using System;
using System.Collections.Generic;
using System.IO;
using Foundation;
using AntiTheftAlarm.iOS.Models;
using System.Text.Json;

namespace AntiTheftAlarm.iOS.Services
{
    /// <summary>
    /// Service for managing app settings and preferences
    /// </summary>
    public class SettingsService
    {
        private const string SettingsFileName = "alarm_settings.json";
        private const string HistoryFileName = "alarm_history.json";
        private readonly string _documentsPath;
        private AlarmSettings _currentSettings;

        public event EventHandler<SettingsChangedEventArgs> SettingsChanged;

        public SettingsService()
        {
            _documentsPath = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments);
            LoadSettings();
        }

        public AlarmSettings CurrentSettings => _currentSettings;

        /// <summary>
        /// Load settings from storage
        /// </summary>
        public void LoadSettings()
        {
            try
            {
                var settingsPath = Path.Combine(_documentsPath, SettingsFileName);
                
                if (File.Exists(settingsPath))
                {
                    var json = File.ReadAllText(settingsPath);
                    _currentSettings = JsonSerializer.Deserialize<AlarmSettings>(json);
                }
                else
                {
                    _currentSettings = new AlarmSettings();
                    SaveSettings();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading settings: {ex.Message}");
                _currentSettings = new AlarmSettings();
            }
        }

        /// <summary>
        /// Save settings to storage
        /// </summary>
        public void SaveSettings()
        {
            try
            {
                var settingsPath = Path.Combine(_documentsPath, SettingsFileName);
                var json = JsonSerializer.Serialize(_currentSettings, new JsonSerializerOptions 
                { 
                    WriteIndented = true 
                });
                
                File.WriteAllText(settingsPath, json);
                SettingsChanged?.Invoke(this, new SettingsChangedEventArgs(_currentSettings));
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error saving settings: {ex.Message}");
            }
        }

        /// <summary>
        /// Update alarm settings
        /// </summary>
        public void UpdateSettings(AlarmSettings newSettings)
        {
            _currentSettings = newSettings;
            SaveSettings();
        }

        /// <summary>
        /// Enable or disable dark mode
        /// </summary>
        public void SetDarkMode(bool enabled)
        {
            _currentSettings.DarkModeEnabled = enabled;
            SaveSettings();
            
            // Apply dark mode immediately
            ApplyDarkMode(enabled);
        }

        /// <summary>
        /// Set custom alarm sound
        /// </summary>
        public void SetAlarmSound(string soundPath)
        {
            if (File.Exists(soundPath))
            {
                _currentSettings.AlarmSoundPath = soundPath;
                SaveSettings();
            }
        }

        /// <summary>
        /// Set alarm volume
        /// </summary>
        public void SetVolume(float volume)
        {
            _currentSettings.Volume = Math.Max(0.0f, Math.Min(1.0f, volume));
            SaveSettings();
        }

        /// <summary>
        /// Set sensitivity level
        /// </summary>
        public void SetSensitivity(SensitivityLevel sensitivity)
        {
            _currentSettings.SensitivityLevel = sensitivity;
            SaveSettings();
        }

        /// <summary>
        /// Enable or disable biometric authentication
        /// </summary>
        public void SetBiometricAuthentication(bool enabled)
        {
            _currentSettings.UseBiometricAuthentication = enabled;
            SaveSettings();
        }

        /// <summary>
        /// Set alarm delay
        /// </summary>
        public void SetAlarmDelay(int delaySeconds)
        {
            _currentSettings.AlarmDelay = Math.Max(0, delaySeconds);
            SaveSettings();
        }

        /// <summary>
        /// Set maximum alarm duration
        /// </summary>
        public void SetMaxAlarmDuration(int durationSeconds)
        {
            _currentSettings.MaxAlarmDuration = Math.Max(0, durationSeconds);
            SaveSettings();
        }

        /// <summary>
        /// Save alarm event to history
        /// </summary>
        public void SaveAlarmEvent(AlarmEvent alarmEvent)
        {
            try
            {
                var history = LoadAlarmHistory();
                history.Add(alarmEvent);
                
                // Keep only last 100 events
                if (history.Count > 100)
                {
                    history.RemoveRange(0, history.Count - 100);
                }
                
                SaveAlarmHistory(history);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error saving alarm event: {ex.Message}");
            }
        }

        /// <summary>
        /// Load alarm history
        /// </summary>
        public List<AlarmEvent> LoadAlarmHistory()
        {
            try
            {
                var historyPath = Path.Combine(_documentsPath, HistoryFileName);
                
                if (File.Exists(historyPath))
                {
                    var json = File.ReadAllText(historyPath);
                    return JsonSerializer.Deserialize<List<AlarmEvent>>(json) ?? new List<AlarmEvent>();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading alarm history: {ex.Message}");
            }
            
            return new List<AlarmEvent>();
        }

        /// <summary>
        /// Save alarm history
        /// </summary>
        private void SaveAlarmHistory(List<AlarmEvent> history)
        {
            try
            {
                var historyPath = Path.Combine(_documentsPath, HistoryFileName);
                var json = JsonSerializer.Serialize(history, new JsonSerializerOptions 
                { 
                    WriteIndented = true 
                });
                
                File.WriteAllText(historyPath, json);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error saving alarm history: {ex.Message}");
            }
        }

        /// <summary>
        /// Clear alarm history
        /// </summary>
        public void ClearAlarmHistory()
        {
            try
            {
                var historyPath = Path.Combine(_documentsPath, HistoryFileName);
                if (File.Exists(historyPath))
                {
                    File.Delete(historyPath);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error clearing alarm history: {ex.Message}");
            }
        }

        /// <summary>
        /// Get available alarm sounds
        /// </summary>
        public List<string> GetAvailableAlarmSounds()
        {
            var sounds = new List<string>();
            
            try
            {
                var soundsPath = Path.Combine(_documentsPath, "Sounds");
                if (Directory.Exists(soundsPath))
                {
                    var soundFiles = Directory.GetFiles(soundsPath, "*.mp3");
                    foreach (var file in soundFiles)
                    {
                        sounds.Add(Path.GetFileName(file));
                    }
                }
                
                // Add default sounds
                if (!sounds.Contains("default_alarm.mp3"))
                {
                    sounds.Insert(0, "default_alarm.mp3");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting available sounds: {ex.Message}");
            }
            
            return sounds;
        }

        /// <summary>
        /// Import custom alarm sound
        /// </summary>
        public bool ImportAlarmSound(string sourcePath, string fileName)
        {
            try
            {
                var soundsPath = Path.Combine(_documentsPath, "Sounds");
                if (!Directory.Exists(soundsPath))
                {
                    Directory.CreateDirectory(soundsPath);
                }
                
                var targetPath = Path.Combine(soundsPath, fileName);
                File.Copy(sourcePath, targetPath, true);
                
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error importing alarm sound: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Reset settings to default
        /// </summary>
        public void ResetToDefaults()
        {
            _currentSettings = new AlarmSettings();
            SaveSettings();
        }

        /// <summary>
        /// Apply dark mode to the app
        /// </summary>
        private void ApplyDarkMode(bool enabled)
        {
            if (UIKit.UIDevice.CurrentDevice.CheckSystemVersion(13, 0))
            {
                var style = enabled ? UIKit.UIUserInterfaceStyle.Dark : UIKit.UIUserInterfaceStyle.Light;
                
                // This would typically be applied to the window or view controller
                // For now, we'll just store the preference
                NSUserDefaults.StandardUserDefaults.SetBool(enabled, "DarkModeEnabled");
            }
        }

        /// <summary>
        /// Export settings and history
        /// </summary>
        public string ExportData()
        {
            try
            {
                var exportData = new
                {
                    Settings = _currentSettings,
                    History = LoadAlarmHistory(),
                    ExportDate = DateTime.Now
                };
                
                return JsonSerializer.Serialize(exportData, new JsonSerializerOptions 
                { 
                    WriteIndented = true 
                });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error exporting data: {ex.Message}");
                return null;
            }
        }
    }

    /// <summary>
    /// Event arguments for settings changes
    /// </summary>
    public class SettingsChangedEventArgs : EventArgs
    {
        public SettingsChangedEventArgs(AlarmSettings settings)
        {
            Settings = settings;
            Timestamp = DateTime.Now;
        }

        public AlarmSettings Settings { get; }
        public DateTime Timestamp { get; }
    }
}
