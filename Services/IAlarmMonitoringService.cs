using System;
using AntiTheftAlarm.iOS.Models;

namespace AntiTheftAlarm.iOS.Services
{
    /// <summary>
    /// Interface for alarm monitoring services
    /// </summary>
    public interface IAlarmMonitoringService
    {
        /// <summary>
        /// Event fired when an alarm is triggered
        /// </summary>
        event EventHandler<AlarmTriggeredEventArgs> AlarmTriggered;

        /// <summary>
        /// Start monitoring for the specific alarm type
        /// </summary>
        /// <param name="alarmType">Type of alarm to monitor</param>
        void StartMonitoring(AlarmType alarmType);

        /// <summary>
        /// Stop monitoring for the specific alarm type
        /// </summary>
        /// <param name="alarmType">Type of alarm to stop monitoring</param>
        void StopMonitoring(AlarmType alarmType);

        /// <summary>
        /// Stop all monitoring
        /// </summary>
        void StopAllMonitoring();

        /// <summary>
        /// Check if monitoring is active for a specific alarm type
        /// </summary>
        /// <param name="alarmType">Type of alarm to check</param>
        /// <returns>True if monitoring is active</returns>
        bool IsMonitoring(AlarmType alarmType);

        /// <summary>
        /// Update sensitivity settings
        /// </summary>
        /// <param name="sensitivity">New sensitivity level</param>
        void UpdateSensitivity(SensitivityLevel sensitivity);
    }

    /// <summary>
    /// Event arguments for alarm triggered events
    /// </summary>
    public class AlarmTriggeredEventArgs : EventArgs
    {
        public AlarmTriggeredEventArgs(AlarmType alarmType, string details = null)
        {
            AlarmType = alarmType;
            Details = details;
            Timestamp = DateTime.Now;
        }

        public AlarmType AlarmType { get; }
        public string Details { get; }
        public DateTime Timestamp { get; }
    }
}
