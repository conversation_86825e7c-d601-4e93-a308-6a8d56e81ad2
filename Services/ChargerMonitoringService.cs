using System;
using Foundation;
using UIKit;
using AntiTheftAlarm.iOS.Models;

namespace AntiTheftAlarm.iOS.Services
{
    /// <summary>
    /// Service for monitoring charger connection/disconnection
    /// </summary>
    public class ChargerMonitoringService : IAlarmMonitoringService
    {
        private bool _isMonitoring;
        private bool _wasChargingWhenStarted;

        public event EventHandler<AlarmTriggeredEventArgs> AlarmTriggered;

        public ChargerMonitoringService()
        {
            // Subscribe to battery state change notifications
            NSNotificationCenter.DefaultCenter.AddObserver(
                UIDevice.BatteryStateDidChangeNotification,
                OnBatteryStateChanged);
        }

        public void StartMonitoring(AlarmType alarmType)
        {
            if (alarmType != AlarmType.ChargerDisconnected)
                return;

            _isMonitoring = true;
            
            // Enable battery monitoring
            UIDevice.CurrentDevice.BatteryMonitoringEnabled = true;
            
            // Record initial charging state
            _wasChargingWhenStarted = IsCurrentlyCharging();
            
            Console.WriteLine($"Charger monitoring started. Initial charging state: {_wasChargingWhenStarted}");
        }

        public void StopMonitoring(AlarmType alarmType)
        {
            if (alarmType != AlarmType.ChargerDisconnected)
                return;

            _isMonitoring = false;
            Console.WriteLine("Charger monitoring stopped");
        }

        public void StopAllMonitoring()
        {
            _isMonitoring = false;
            UIDevice.CurrentDevice.BatteryMonitoringEnabled = false;
        }

        public bool IsMonitoring(AlarmType alarmType)
        {
            return alarmType == AlarmType.ChargerDisconnected && _isMonitoring;
        }

        public void UpdateSensitivity(SensitivityLevel sensitivity)
        {
            // Charger monitoring doesn't use sensitivity levels
        }

        private void OnBatteryStateChanged(NSNotification notification)
        {
            if (!_isMonitoring)
                return;

            var currentlyCharging = IsCurrentlyCharging();
            
            // Trigger alarm if charger was connected when monitoring started
            // and is now disconnected
            if (_wasChargingWhenStarted && !currentlyCharging)
            {
                var details = $"Charger disconnected. Battery level: {UIDevice.CurrentDevice.BatteryLevel * 100:F1}%";
                AlarmTriggered?.Invoke(this, new AlarmTriggeredEventArgs(AlarmType.ChargerDisconnected, details));
                Console.WriteLine($"Charger alarm triggered: {details}");
            }
        }

        private bool IsCurrentlyCharging()
        {
            var batteryState = UIDevice.CurrentDevice.BatteryState;
            return batteryState == UIDeviceBatteryState.Charging || 
                   batteryState == UIDeviceBatteryState.Full;
        }

        ~ChargerMonitoringService()
        {
            NSNotificationCenter.DefaultCenter.RemoveObserver(this);
        }
    }
}
