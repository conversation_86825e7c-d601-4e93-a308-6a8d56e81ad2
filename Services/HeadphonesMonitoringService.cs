using System;
using Foundation;
using AVFoundation;
using AntiTheftAlarm.iOS.Models;

namespace AntiTheftAlarm.iOS.Services
{
    /// <summary>
    /// Service for monitoring headphones connection/disconnection
    /// </summary>
    public class HeadphonesMonitoringService : IAlarmMonitoringService
    {
        private bool _isMonitoring;
        private bool _headphonesConnectedWhenStarted;

        public event EventHandler<AlarmTriggeredEventArgs> AlarmTriggered;

        public HeadphonesMonitoringService()
        {
            // Subscribe to audio route change notifications
            NSNotificationCenter.DefaultCenter.AddObserver(
                AVAudioSession.RouteChangeNotification,
                OnAudioRouteChanged);
        }

        public void StartMonitoring(AlarmType alarmType)
        {
            if (alarmType != AlarmType.HeadphonesDisconnected)
                return;

            _isMonitoring = true;
            
            // Record initial headphones state
            _headphonesConnectedWhenStarted = AreHeadphonesConnected();
            
            Console.WriteLine($"Headphones monitoring started. Initial state: {(_headphonesConnectedWhenStarted ? "Connected" : "Disconnected")}");
        }

        public void StopMonitoring(AlarmType alarmType)
        {
            if (alarmType != AlarmType.HeadphonesDisconnected)
                return;

            _isMonitoring = false;
            Console.WriteLine("Headphones monitoring stopped");
        }

        public void StopAllMonitoring()
        {
            _isMonitoring = false;
        }

        public bool IsMonitoring(AlarmType alarmType)
        {
            return alarmType == AlarmType.HeadphonesDisconnected && _isMonitoring;
        }

        public void UpdateSensitivity(SensitivityLevel sensitivity)
        {
            // Headphones monitoring doesn't use sensitivity levels
        }

        private void OnAudioRouteChanged(NSNotification notification)
        {
            if (!_isMonitoring)
                return;

            var userInfo = notification.UserInfo;
            if (userInfo == null)
                return;

            // Get the reason for the route change
            var reasonValue = userInfo[AVAudioSession.RouteChangeReasonKey] as NSNumber;
            if (reasonValue == null)
                return;

            var reason = (AVAudioSessionRouteChangeReason)reasonValue.Int32Value;
            var currentlyConnected = AreHeadphonesConnected();

            // Check if headphones were disconnected
            if (_headphonesConnectedWhenStarted && !currentlyConnected)
            {
                var reasonText = GetReasonText(reason);
                var details = $"Headphones disconnected. Reason: {reasonText}";
                
                AlarmTriggered?.Invoke(this, new AlarmTriggeredEventArgs(AlarmType.HeadphonesDisconnected, details));
                Console.WriteLine($"Headphones alarm triggered: {details}");
            }
        }

        private bool AreHeadphonesConnected()
        {
            var audioSession = AVAudioSession.SharedInstance();
            var currentRoute = audioSession.CurrentRoute;

            foreach (var output in currentRoute.Outputs)
            {
                var portType = output.PortType;
                
                // Check for various headphone types
                if (portType == AVAudioSessionPort.HeadphonesAndMicrophone ||
                    portType == AVAudioSessionPort.Headphones ||
                    portType == AVAudioSessionPort.BluetoothA2DP ||
                    portType == AVAudioSessionPort.BluetoothHFP ||
                    portType == AVAudioSessionPort.BluetoothLE)
                {
                    return true;
                }
            }

            return false;
        }

        private string GetReasonText(AVAudioSessionRouteChangeReason reason)
        {
            return reason switch
            {
                AVAudioSessionRouteChangeReason.Unknown => "Unknown",
                AVAudioSessionRouteChangeReason.NewDeviceAvailable => "New Device Available",
                AVAudioSessionRouteChangeReason.OldDeviceUnavailable => "Device Disconnected",
                AVAudioSessionRouteChangeReason.CategoryChange => "Category Change",
                AVAudioSessionRouteChangeReason.Override => "Override",
                AVAudioSessionRouteChangeReason.WakeFromSleep => "Wake From Sleep",
                AVAudioSessionRouteChangeReason.NoSuitableRouteForCategory => "No Suitable Route",
                AVAudioSessionRouteChangeReason.RouteConfigurationChange => "Route Configuration Change",
                _ => reason.ToString()
            };
        }

        ~HeadphonesMonitoringService()
        {
            NSNotificationCenter.DefaultCenter.RemoveObserver(this);
        }
    }
}
