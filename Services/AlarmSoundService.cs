using System;
using System.IO;
using Foundation;
using AVFoundation;
using UIKit;
using AntiTheftAlarm.iOS.Models;

namespace AntiTheftAlarm.iOS.Services
{
    /// <summary>
    /// Service for playing alarm sounds at maximum volume
    /// </summary>
    public class AlarmSoundService
    {
        private AVAudioPlayer _audioPlayer;
        private AVAudioSession _audioSession;
        private bool _isPlaying;
        private float _originalVolume;
        private NSTimer _volumeMonitorTimer;

        public event EventHandler<AlarmSoundEventArgs> SoundStarted;
        public event EventHandler<AlarmSoundEventArgs> SoundStopped;

        public AlarmSoundService()
        {
            _audioSession = AVAudioSession.SharedInstance();
            SetupAudioSession();
        }

        public bool IsPlaying => _isPlaying;

        private void SetupAudioSession()
        {
            try
            {
                // Configure audio session for alarm playback
                var error = _audioSession.SetCategory(
                    AVAudioSessionCategory.Playback,
                    AVAudioSessionCategoryOptions.MixWithOthers | 
                    AVAudioSessionCategoryOptions.DuckOthers |
                    AVAudioSessionCategoryOptions.DefaultToSpeaker);

                if (error != null)
                {
                    Console.WriteLine($"Audio session category error: {error.LocalizedDescription}");
                }

                // Set mode for alarm
                error = _audioSession.SetMode(AVAudioSessionMode.Default);
                if (error != null)
                {
                    Console.WriteLine($"Audio session mode error: {error.LocalizedDescription}");
                }

                // Activate the session
                _audioSession.SetActive(true);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Audio session setup failed: {ex.Message}");
            }
        }

        public void PlayAlarm(string soundFileName = null)
        {
            if (_isPlaying)
                return;

            try
            {
                // Store original volume
                _originalVolume = _audioSession.OutputVolume;

                // Get sound file path
                var soundPath = GetSoundFilePath(soundFileName ?? "default_alarm.mp3");
                
                if (!File.Exists(soundPath))
                {
                    // Create a default alarm sound if file doesn't exist
                    CreateDefaultAlarmSound();
                    soundPath = GetSoundFilePath("default_alarm.mp3");
                }

                // Create audio player
                var soundUrl = NSUrl.FromFilename(soundPath);
                _audioPlayer = AVAudioPlayer.FromUrl(soundUrl);
                
                if (_audioPlayer == null)
                {
                    Console.WriteLine("Failed to create audio player");
                    return;
                }

                // Configure audio player for alarm
                _audioPlayer.NumberOfLoops = -1; // Loop indefinitely
                _audioPlayer.Volume = 1.0f; // Maximum volume
                _audioPlayer.PrepareToPlay();

                // Force maximum system volume
                ForceMaximumVolume();

                // Start playing
                _audioPlayer.Play();
                _isPlaying = true;

                // Start monitoring volume to prevent user from lowering it
                StartVolumeMonitoring();

                // Trigger vibration
                TriggerVibration();

                SoundStarted?.Invoke(this, new AlarmSoundEventArgs(soundFileName));
                Console.WriteLine($"Alarm sound started: {soundFileName}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to play alarm: {ex.Message}");
            }
        }

        public void StopAlarm()
        {
            if (!_isPlaying)
                return;

            try
            {
                // Stop volume monitoring
                StopVolumeMonitoring();

                // Stop audio player
                _audioPlayer?.Stop();
                _audioPlayer?.Dispose();
                _audioPlayer = null;

                _isPlaying = false;

                SoundStopped?.Invoke(this, new AlarmSoundEventArgs(null));
                Console.WriteLine("Alarm sound stopped");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to stop alarm: {ex.Message}");
            }
        }

        private void ForceMaximumVolume()
        {
            try
            {
                // Note: iOS doesn't allow apps to directly change system volume
                // But we can ensure our audio session is configured for maximum impact
                _audioSession.SetCategory(
                    AVAudioSessionCategory.Playback,
                    AVAudioSessionCategoryOptions.DefaultToSpeaker);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to force maximum volume: {ex.Message}");
            }
        }

        private void StartVolumeMonitoring()
        {
            // Monitor volume changes and alert if user tries to lower it
            _volumeMonitorTimer = NSTimer.CreateRepeatingScheduledTimer(0.5, timer =>
            {
                if (_isPlaying && _audioPlayer != null)
                {
                    // Ensure our player volume stays at maximum
                    _audioPlayer.Volume = 1.0f;
                    
                    // Check if system volume is too low
                    var currentVolume = _audioSession.OutputVolume;
                    if (currentVolume < 0.8f)
                    {
                        // Show alert or notification about volume
                        ShowVolumeWarning();
                    }
                }
            });
        }

        private void StopVolumeMonitoring()
        {
            _volumeMonitorTimer?.Invalidate();
            _volumeMonitorTimer = null;
        }

        private void ShowVolumeWarning()
        {
            // This would typically show a notification or alert
            // For now, just log it
            Console.WriteLine("Warning: System volume is low. Alarm may not be audible.");
        }

        private void TriggerVibration()
        {
            // Trigger device vibration
            var impact = new UIImpactFeedbackGenerator(UIImpactFeedbackStyle.Heavy);
            impact.ImpactOccurred();

            // Continue vibrating while alarm is playing
            var vibrationTimer = NSTimer.CreateRepeatingScheduledTimer(1.0, timer =>
            {
                if (_isPlaying)
                {
                    var vibration = new UIImpactFeedbackGenerator(UIImpactFeedbackStyle.Heavy);
                    vibration.ImpactOccurred();
                }
                else
                {
                    timer.Invalidate();
                }
            });
        }

        private string GetSoundFilePath(string fileName)
        {
            var documentsPath = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments);
            var soundsPath = Path.Combine(documentsPath, "Sounds");
            
            if (!Directory.Exists(soundsPath))
            {
                Directory.CreateDirectory(soundsPath);
            }
            
            return Path.Combine(soundsPath, fileName);
        }

        private void CreateDefaultAlarmSound()
        {
            try
            {
                // Create a simple alarm sound programmatically
                // This is a placeholder - in a real app, you'd include actual sound files
                var soundPath = GetSoundFilePath("default_alarm.mp3");
                
                // For now, we'll use a system sound
                // In production, include actual alarm sound files in the app bundle
                Console.WriteLine($"Default alarm sound would be created at: {soundPath}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to create default alarm sound: {ex.Message}");
            }
        }

        public void SetCustomAlarmSound(string soundFilePath)
        {
            if (File.Exists(soundFilePath))
            {
                var targetPath = GetSoundFilePath(Path.GetFileName(soundFilePath));
                File.Copy(soundFilePath, targetPath, true);
                Console.WriteLine($"Custom alarm sound set: {targetPath}");
            }
        }

        public void Dispose()
        {
            StopAlarm();
            _audioPlayer?.Dispose();
        }
    }

    /// <summary>
    /// Event arguments for alarm sound events
    /// </summary>
    public class AlarmSoundEventArgs : EventArgs
    {
        public AlarmSoundEventArgs(string soundFileName)
        {
            SoundFileName = soundFileName;
            Timestamp = DateTime.Now;
        }

        public string SoundFileName { get; }
        public DateTime Timestamp { get; }
    }
}
