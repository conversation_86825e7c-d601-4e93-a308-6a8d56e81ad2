using System;
using CoreMotion;
using Foundation;
using AntiTheftAlarm.iOS.Models;
using AntiTheftAlarm.iOS.Configuration;

namespace AntiTheftAlarm.iOS.Services
{
    /// <summary>
    /// Service for monitoring device movement using accelerometer and gyroscope
    /// </summary>
    public class MotionMonitoringService : IAlarmMonitoringService
    {
        private readonly CMMotionManager _motionManager;
        private bool _isMonitoring;
        private MotionData _baselineMotion;
        private SensitivityLevel _sensitivity = SensitivityLevel.Medium;
        private NSTimer _motionTimer;

        public event EventHandler<AlarmTriggeredEventArgs> AlarmTriggered;

        public MotionMonitoringService()
        {
            _motionManager = new CMMotionManager();
        }

        public void StartMonitoring(AlarmType alarmType)
        {
            if (alarmType != AlarmType.DeviceMovement && alarmType != AlarmType.PocketRemoval)
                return;

            if (!_motionManager.AccelerometerAvailable || !_motionManager.GyroAvailable)
            {
                Console.WriteLine("Motion sensors not available");
                return;
            }

            _isMonitoring = true;
            
            // Set update interval based on sensitivity
            var updateInterval = GetUpdateInterval();
            _motionManager.AccelerometerUpdateInterval = updateInterval;
            _motionManager.GyroUpdateInterval = updateInterval;

            // Start motion updates
            _motionManager.StartAccelerometerUpdates();
            _motionManager.StartGyroUpdates();

            // Record baseline motion after a short delay
            NSTimer.CreateScheduledTimer(2.0, false, timer =>
            {
                RecordBaselineMotion();
                StartMotionMonitoring();
            });

            Console.WriteLine($"Motion monitoring started for {alarmType}");
        }

        public void StopMonitoring(AlarmType alarmType)
        {
            if (alarmType != AlarmType.DeviceMovement && alarmType != AlarmType.PocketRemoval)
                return;

            StopAllMonitoring();
        }

        public void StopAllMonitoring()
        {
            _isMonitoring = false;
            _motionTimer?.Invalidate();
            _motionTimer = null;
            
            _motionManager.StopAccelerometerUpdates();
            _motionManager.StopGyroUpdates();
            
            Console.WriteLine("Motion monitoring stopped");
        }

        public bool IsMonitoring(AlarmType alarmType)
        {
            return (alarmType == AlarmType.DeviceMovement || alarmType == AlarmType.PocketRemoval) && _isMonitoring;
        }

        public void UpdateSensitivity(SensitivityLevel sensitivity)
        {
            _sensitivity = sensitivity;
            
            if (_isMonitoring)
            {
                // Update motion manager intervals
                var updateInterval = GetUpdateInterval();
                _motionManager.AccelerometerUpdateInterval = updateInterval;
                _motionManager.GyroUpdateInterval = updateInterval;
            }
        }

        private void RecordBaselineMotion()
        {
            if (_motionManager.AccelerometerData != null && _motionManager.GyroData != null)
            {
                _baselineMotion = new MotionData
                {
                    AccelerationX = _motionManager.AccelerometerData.Acceleration.X,
                    AccelerationY = _motionManager.AccelerometerData.Acceleration.Y,
                    AccelerationZ = _motionManager.AccelerometerData.Acceleration.Z,
                    RotationX = _motionManager.GyroData.RotationRate.X,
                    RotationY = _motionManager.GyroData.RotationRate.Y,
                    RotationZ = _motionManager.GyroData.RotationRate.Z,
                    Timestamp = DateTime.Now
                };
                
                Console.WriteLine($"Baseline motion recorded: Accel={_baselineMotion.AccelerationMagnitude:F3}, Rotation={_baselineMotion.RotationMagnitude:F3}");
            }
        }

        private void StartMotionMonitoring()
        {
            var checkInterval = GetCheckInterval();
            _motionTimer = NSTimer.CreateRepeatingScheduledTimer(checkInterval, CheckMotionChanges);
        }

        private void CheckMotionChanges()
        {
            if (!_isMonitoring || _baselineMotion == null)
                return;

            if (_motionManager.AccelerometerData == null || _motionManager.GyroData == null)
                return;

            var currentMotion = new MotionData
            {
                AccelerationX = _motionManager.AccelerometerData.Acceleration.X,
                AccelerationY = _motionManager.AccelerometerData.Acceleration.Y,
                AccelerationZ = _motionManager.AccelerometerData.Acceleration.Z,
                RotationX = _motionManager.GyroData.RotationRate.X,
                RotationY = _motionManager.GyroData.RotationRate.Y,
                RotationZ = _motionManager.GyroData.RotationRate.Z,
                Timestamp = DateTime.Now
            };

            // Calculate differences from baseline
            var accelDiff = Math.Abs(currentMotion.AccelerationMagnitude - _baselineMotion.AccelerationMagnitude);
            var rotationDiff = Math.Abs(currentMotion.RotationMagnitude - _baselineMotion.RotationMagnitude);

            // Get thresholds based on sensitivity
            var (accelThreshold, rotationThreshold) = GetMotionThresholds();

            if (accelDiff > accelThreshold || rotationDiff > rotationThreshold)
            {
                // Determine if this is device movement or pocket removal based on motion pattern
                var alarmType = DetermineAlarmType(currentMotion, accelDiff, rotationDiff);
                var details = $"Motion detected - Accel diff: {accelDiff:F3}, Rotation diff: {rotationDiff:F3}";
                AlarmTriggered?.Invoke(this, new AlarmTriggeredEventArgs(alarmType, details));
                Console.WriteLine($"{alarmType} alarm triggered: {details}");
            }
        }

        private AlarmType DetermineAlarmType(MotionData currentMotion, double accelDiff, double rotationDiff)
        {
            // If acceleration change is significantly higher than rotation, likely pocket removal
            // If both are high, likely general device movement
            if (accelDiff > rotationDiff * 2 && currentMotion.AccelerationMagnitude > 1.5)
            {
                return AlarmType.PocketRemoval;
            }

            return AlarmType.DeviceMovement;
        }

        private double GetUpdateInterval()
        {
            return AppConfig.GetUpdateInterval(_sensitivity);
        }

        private double GetCheckInterval()
        {
            return AppConfig.GetCheckInterval(_sensitivity);
        }

        private (double accelThreshold, double rotationThreshold) GetMotionThresholds()
        {
            var accelThreshold = AppConfig.GetMotionThreshold(_sensitivity, true);
            var rotationThreshold = AppConfig.GetMotionThreshold(_sensitivity, false);
            return (accelThreshold, rotationThreshold);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (disposing)
            {
                StopAllMonitoring();
                _motionManager?.Dispose();
            }
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }
    }
}
